package com.isoftstone.hig.supervise.api.filter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * @Copyright:   Copyright(c) 2020 ISoftstone  Co. Ltd. All Rights Reserved.
 * @Description:            车辆信息上报   搜索条件实体类
 * @Version:     1.0
 * <AUTHOR> WTLip
 * @date:   2020年7月27日
 */
@Data
public class VehicleBusSearchVo implements Serializable{

    private static final long serialVersionUID = 1L;

    private String vehicleNumber;

    private String vehiclePlateColorCode;

    private String vehicleType;

    private String owner;

    private String sendToProDateTimeFrom;

    private String sendToProDateTimeTo;

    private String status;

    //id集合
    private List<String> idList;

    //状态集合
    private List<String> statusList;

    //车辆类型【select:1-整车,2-车头,3-挂车】
    private String carType;

    //挂车车牌号
    private String trailerVehiclePlateNumber;

    /**
     * 数据添加标识【10-导入；20-非导入】
     */
    @ApiModelProperty(value = "添加源头", name = "dataAddTag")
    private String dataAddTag;

    private Long id;

    @ApiModelProperty(value = "网络货运主体id", name = "networkMainBodyId")
    private String networkMainBodyId;

    //主体上报时间从
    @ApiModelProperty(value = "主体上报时间从", name = "netValidReportStart")
    private String netValidReportStart;

    //主体上报时间至
    @ApiModelProperty(value = "主体上报时间至", name = "netValidReportEnd")
    private String netValidReportEnd;

    public List<String> getIdList() {
        return idList;
    }

    public void setIdList(List<String> idList) {
        this.idList = idList;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public String getVehicleNumber() {
        return vehicleNumber;
    }

    public void setVehicleNumber(String vehicleNumber) {
        this.vehicleNumber = vehicleNumber;
    }

    public String getVehiclePlateColorCode() {
        return vehiclePlateColorCode;
    }

    public void setVehiclePlateColorCode(String vehiclePlateColorCode) {
        this.vehiclePlateColorCode = vehiclePlateColorCode;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getSendToProDateTimeFrom() {
        return sendToProDateTimeFrom;
    }

    public void setSendToProDateTimeFrom(String sendToProDateTimeFrom) {
        this.sendToProDateTimeFrom = sendToProDateTimeFrom;
    }

    public String getSendToProDateTimeTo() {
        return sendToProDateTimeTo;
    }

    public void setSendToProDateTimeTo(String sendToProDateTimeTo) {
        this.sendToProDateTimeTo = sendToProDateTimeTo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getTrailerVehiclePlateNumber() {
        return trailerVehiclePlateNumber;
    }

    public void setTrailerVehiclePlateNumber(String trailerVehiclePlateNumber) {
        this.trailerVehiclePlateNumber = trailerVehiclePlateNumber;
    }

    public String getDataAddTag() {
        return dataAddTag;
    }

    public void setDataAddTag(String dataAddTag) {
        this.dataAddTag = dataAddTag;
    }
}
