package com.isoftstone.hig.supervise.api.service;

import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.supervise.api.entity.ChannelVO;
import com.isoftstone.hig.supervise.api.entity.MessageNote;
import com.isoftstone.hig.supervise.api.entity.ReportMsgVO;

import java.util.List;
import java.util.Map;

public interface MessageNoteService {

    /**
     * 处理上报结果
     *
     * @param reportMsgVO 上报信息
     */
    void handleReportResults(ReportMsgVO reportMsgVO);


    /**
     * 插入上报记录
     *
     * @param messageNote 消息
     * @return int
     */
    int insertReportMsg(MessageNote messageNote);

    /**
     * 报文分页列表
     * @param pageInfo
     * @return
     */
    ResultMode<MessageNote> queryMessagePageInfo(PagingInfo<MessageNote> pageInfo);

    /**
     * 校验数据是否已经上传
     * @return
     */
    int checkIsUploadData(String messageContentId,String messageType,String networkMainBodyId);


    /**
     * 批量校验数据是否已经上传
     */
    List<String> checkIsUploadDatas(List<String> messageContentIdList, String messageType, String networkMainBodyId);


    /**
     * 检查司机是否上报
     *
     * @param drivingLicense    身份证号
     * @param networkMainBodyId 网络主体id
     * @return int
     */
    int checkIsDriverReport(String drivingLicense, String networkMainBodyId);

    /**
     * 检查司机是否上报 +delay
     * @param drivingLicense
     * @param networkMainBodyId
     * @param delay
     * @return
     */
    int checkIsDriverReportWitchDelay(String drivingLicense, String networkMainBodyId,Integer delay);

    /**
     * 查询上报信息
     *
     * @param messageContentId  业务id
     * @param messageType       业务类型
     * @param networkMainBodyId 网络主体id
     * @return {@link List}<{@link MessageNote}>
     */
    List<MessageNote> queryMessageNoteByBusiId(String messageContentId,String messageType,String networkMainBodyId);


    /**
     * 查询上报的报文
     *
     * @param messageNote 消息指出
     * @return {@link ResultMode}<{@link MessageNote}>
     */
    ResultMode<MessageNote> queryReportMessage(MessageNote messageNote);

    /**
     * 查询返回信息
     *
     * @param messageContentId  消息内容id
     * @param messageType       消息类型
     * @param networkMainBodyId 主体id
     * @return {@link List}<{@link String}>
     */
    List<MessageNote> queryReturnMsgByBusiId(String messageContentId, String messageType, String networkMainBodyId);


    /**
     * 查询网络货运主体
     *
     * @param channel 通道
     * @return {@link ResultMode}<{@link ChannelVO}>
     */
    ResultMode<ChannelVO> queryChannel(MessageNote channel);

    /**
     * 统计上报成功数量
     *
     * @param messageContentId 消息内容id
     * @param messageType      消息类型
     * @return int
     */
    int countSucReportNum(String messageContentId,String messageType);


    /**
     * 获取网络货运主体状态
     *
     * @param messageContentId  消息内容id
     * @param messageType       消息类型
     * @return {@link Map}<{@link String},{@link String}>
     */
    Map<String,String> getNetworkMainBodyStatus(String messageContentId, String messageType);


    /**
     * 检查网络主体状态
     *
     * @param messageContentId 消息内容id
     * @param messageType      消息类型
     * @param networkIdList    网络id列表
     * @return {@link ResultMode}
     */
    ResultMode checkNetworkMainBodyStatus(String messageContentId, String messageType,List<String> networkIdList);


    /**
     * 同步消息到dcs
     *
     * @return {@link ResultMode}
     */
    ResultMode syncMessageToDcs();

    /**
     * 删除上报消息
     *
     * @param messageContentId 消息id
     * @return int
     */
    int deleteMessageNoteByMessageContentId(String messageContentId,String messageType);

}
