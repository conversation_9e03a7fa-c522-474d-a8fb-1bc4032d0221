package com.isoftstone.hig.supervise.api.service;

import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.supervise.api.entity.ReportMsgVO;
import com.isoftstone.hig.supervise.api.entity.SyncCapitalFlowVo;
import com.isoftstone.hig.supervise.api.entity.Waybill;
import com.isoftstone.hig.supervise.api.entity.WaybillAttribute;
import com.isoftstone.hig.supervise.api.filter.WaybillSearchVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface WaybillService {

    /**
     * 新增运单信息
     * @param waybill
     */
    void addWaybillInfo(Waybill waybill)throws Exception;

    /**
     *  车辆业务信息  分页列表
     * @param pageInfo
     * @return
     */
    ResultMode<Waybill> qureyWaybillBusPageInfo(PagingInfo<WaybillSearchVo> pageInfo)throws Exception;

    /**
     * 删除电子运单
     * @param waybill
     * @throws Exception
     */
    ResultMode<Waybill> deleteWaybillInfo(Waybill waybill) throws Exception;

    /**
     * 保存电子运单信息
     * @param waybill
     */
    ResultMode<Waybill> saveWaybillInfo(Waybill waybill) throws Exception;

    /**
     * 提交电子运单信息
     * @param waybill
     * @throws Exception
     */
    ResultMode<String> submitWaybill(Waybill waybill) throws Exception;

    /**
     * 查看电子运单详情
     * @param waybill
     * @return
     */
    ResultMode<Waybill> viewWaybillBusDetail(Waybill waybill)throws Exception;

    /**
     * 查看电子运单映射结果
     * @param waybill
     * @return
     */
    ResultMode<Waybill> viewWaybillReportDetail(Waybill waybill)throws Exception;

    /**
     * 批量上报电子运单信息
     * @param waybillIdList
     * @return
     */
    ResultMode<String> batchReportWaybill(List<String> waybillIdList) throws Exception;

    /**
     * 更新上报状态
     *
     * @param reportMsg 运单
     */
    void updateReportStatus(ReportMsgVO reportMsg);


    /**
     * 查询运单
     *
     * @param shippingNoteNumber
     * @return {@link Waybill}
     */
    Waybill queryWaybilByShippingNumber(String shippingNoteNumber);

    /**
     * 修改运单的上报状态为已删除
     *
     * @param waybillId
     * @return
     */
    Boolean updateWaybillBusInfoStatusByWaybillId(String waybillId);

    /**
     * 查询上报运单数据
     *
     * @return {@link List}<{@link Waybill}>
     */
    List<Waybill> queryReportWaybill(Integer start, Integer end, Map<String,Object> paramMap);

    /**
     * 更新推送数据（终止支付金额），条件为上报成功(status=4)之外的其他条件
     *
     * @param waybillId 运单号
     * @param amount 金额
     * */
    void updateTerminationWaybillAmountByWaybillId(String waybillId, BigDecimal amount) ;

    /**
     * 更新运单信息
     *
     * @param waybill
     * @return void
     * <AUTHOR>
     * @Date 2023/9/5 18:57
     */
    void updateWaybill(Waybill waybill);

    /**
     * 同步监管运单信息到 ES
     *
     * @param waybill
     * @return void
     * <AUTHOR>
     * @Date 2023/9/12 17:08
     */
    void synWaybillToEs(Waybill waybill);

    /**
     * 根据运单到获取运单属性
     * @param waybillId
     * @return
     */
    WaybillAttribute getWaybillAttributeByWaybillId(String waybillId);

    /**
     * 电子运单自动上报
     * @param custIds 网络货运主体
     * @param map  上报条件
     */
    void autoReportWaybill(List<String> custIds, Map<String, Object> map);

    /**
     * 电子运单信息重新上报-保存并上报
     */
    ResultMode reportAgain(Waybill waybill);

    /**
     * ToB业务资金同步
     * @param capitalFlowVo
     */
    void handleCapitalFlowMsg(SyncCapitalFlowVo capitalFlowVo);

    /**
     * 查询运单属性集合
     */
    List<WaybillAttribute> queryWaybillAttributeByShippingNumberList(List<String> shippingNoteNumberList);

    /**
     * 失败数据自动上报
     */
    void autoReportError(List<String> custIds);

    /**
     * 查询运单集合
     */
    List<Waybill> queryWaybillByShippingNumberList(List<String> shippingNoteNumberList);

    void init(Waybill initWaybill);
}
