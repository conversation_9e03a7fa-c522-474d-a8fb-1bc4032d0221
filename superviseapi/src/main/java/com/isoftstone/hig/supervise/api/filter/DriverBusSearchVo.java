package com.isoftstone.hig.supervise.api.filter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * @Copyright:   Copyright(c) 2020 ISoftstone  Co. Ltd. All Rights Reserved.
 * @Description:            司机上报列表搜索条件
 * @Version:     1.0
 * <AUTHOR> WTLip
 * @date:   2020年7月24日
 */
@Data
@ApiModel(description = "司机上报列表搜索条件")
public class DriverBusSearchVo implements Serializable{

    private static final long serialVersionUID = 1L;

    //司机名字
    @ApiModelProperty(name="driverName")
    private String driverName;

    //身份证号
    @ApiModelProperty(name="drivingLicense")
    private String drivingLicense;

    //手机号
    @ApiModelProperty(name="telephone")
    private String telephone;

    //上报时间从
    @ApiModelProperty(value = "上报时间从", name = "validReportFrom")
    private String validReportStart;

    //上报时间至
    @ApiModelProperty(value = "上报时间至", name = "validReportTo")
    private String validReportEnd;

    //上报状态
    //状态：0：删除，1：待处理，2：已保存，3：待上报，4上报成功，5上报失败
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    /**
     * 数据添加标识【10-导入；20-非导入】
     */
    @ApiModelProperty(value = "添加源头", name = "dataAddTag")
    private String dataAddTag;

    //id集合
    private List<String> idList;

    //状态集合
    private List<String> statusList;

    @ApiModelProperty(value = "网络货运主体id", name = "networkMainBodyId")
    private String networkMainBodyId;

    //主体上报时间从
    @ApiModelProperty(value = "主体上报时间从", name = "netValidReportStart")
    private String netValidReportStart;

    //主体上报时间至
    @ApiModelProperty(value = "主体上报时间至", name = "netValidReportEnd")
    private String netValidReportEnd;

    public List<String> getIdList() {
        return idList;
    }

    public void setIdList(List<String> idList) {
        this.idList = idList;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDrivingLicense() {
        return drivingLicense;
    }

    public void setDrivingLicense(String drivingLicense) {
        this.drivingLicense = drivingLicense;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getValidReportStart() {
        return validReportStart;
    }

    public void setValidReportStart(String validReportStart) {
        this.validReportStart = validReportStart;
    }

    public String getValidReportEnd() {
        return validReportEnd;
    }

    public void setValidReportEnd(String validReportEnd) {
        this.validReportEnd = validReportEnd;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDataAddTag() {
        return dataAddTag;
    }

    public void setDataAddTag(String dataAddTag) {
        this.dataAddTag = dataAddTag;
    }
}
