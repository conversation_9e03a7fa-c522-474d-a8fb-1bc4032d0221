package com.isoftstone.hig.supervise.api.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(description = "主键id,车辆信息")
public class Vehicle  extends BaseRabbitMessageVO implements Serializable {

    private String id;

    //车牌号
    @ApiModelProperty(value = "车牌号", name = "vehicleNumber")
    private String vehicleNumber;

    //车辆颜色代码
    @ApiModelProperty(value = "车辆颜色代码", name = "vehiclePlateColorCode")
    private String vehiclePlateColorCode;

    //车辆类型
    @ApiModelProperty(value = "车辆类型", name = "vehicleType")
    private String vehicleType;

    //所有人
    @ApiModelProperty(value = "所有人", name = "owner")
    private String owner;

    //使用性质
    @ApiModelProperty(value = "使用性质", name = "useCharacter")
    private String useCharacter;

    //车辆识别代号
    @ApiModelProperty(value = "车辆识别代号", name = "VIN")
    private String vin;

    //发证机关
    @ApiModelProperty(value = "发证机关", name = "issuingOrganizations")
    private String issuingOrganizations;

    //注册日期
    @ApiModelProperty(value = "注册日期", name = "registerDate")
    private String registerDate;

    //发证日期
    @ApiModelProperty(value = "发证日期", name = "issueDate")
    private String issueDate;

    //车辆能源类型
    @ApiModelProperty(value = "车辆能源类型", name = "vehicleEnergyType")
    private String vehicleEnergyType;

    //核定载质量
    @ApiModelProperty(value = "核定载质量", name = "vehicleTonnage")
    private String vehicleTonnage;

    //吨位
    @ApiModelProperty(value = "吨位", name = "grossMass")
    private String grossMass;

    //道路运输证号
    @ApiModelProperty(value = "道路运输证号", name = "roadTransportCertificateNumber")
    private String roadTransportCertificateNumber;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "挂车牌照号", name = "trailerVehiclePlateNumber")
    private String trailerVehiclePlateNumber;

    @ApiModelProperty(value = "挂车牌照颜色", name = "trailerVehiclePlateColorCode")
    private String trailerVehiclePlateColorCode;

    @ApiModelProperty(value = "车辆类型【select:1-整车,2-车头,3-挂车】", name = "carType")
    private String carType;

    //状态：0：删除，1：待处理，2：已保存，3：待上报，4上报成功，5上报失败
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    //上报报文
    @ApiModelProperty(value = "上报报文", name = "reportMessage")
    private String reportMessage;

    //返回报文
    @ApiModelProperty(value = "返回报文", name = "resultMessage")
    private String resultMessage;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", name = "modifyBy")
    private String modifyBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "modifyDate")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyDate;

    /**
     * 上报时间
     */
    @ApiModelProperty(value = "上报时间", name = "sendToProDateTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendToProDateTime;

    /**
     *预留字段1
     */
    @ApiModelProperty(value = "预留字段1", name = "item1")
    private String item1;
    /**
     *预留字段2
     */
    @ApiModelProperty(value = "预留字段2", name = "item2")
    private String item2;
    /**
     *预留字段3
     */
    @ApiModelProperty(value = "预留字段3", name = "item3")
    private String item3;
    /**
     *预留字段4
     */
    @ApiModelProperty(value = "预留字段4", name = "item4")
    private String item4;

    @ApiModelProperty(value = "车辆Id集合", name = "vehicleIdList")
    private List<String> vehicleIdList;

    /**
     * true为是，false为否
     */
    @ApiModelProperty(value = "是否多次上传", name = "againReport")
    private boolean againReport = false;

    @ApiModelProperty(value = "本次上报数量", name = "reportNum")
    private Integer reportNum;

    @ApiModelProperty(value = "已完成上报数量", name = "finNum")
    private Integer finNum;

    @ApiModelProperty("返回报文")
    private String successMessage;


    @ApiModelProperty(value = "状态类型")
    private String statusType;
    /*-----------------------------------------小快新增字段----------------------------------------------*/

    /**
     *行驶证主页
     */
    @ApiModelProperty(value = "行驶证主页", name = "licenseHomepage")
    private String licenseHomepage;

    /**
     *行驶证副业
     */
    @ApiModelProperty(value = "行驶证副业", name = "licenseAuxiliaryPage")
    private String licenseAuxiliaryPage;

    /**
     *业主名称
     */
    @ApiModelProperty(value = "业主名称", name = "businessHouseholdsName")
    private String businessHouseholdsName;

    /**
     *挂车车牌号
     */
    @ApiModelProperty(value = "挂车车牌号", name = "trailerPlateNo")
    private String trailerPlateNo;

    /**
     *档案编号
     */
    @ApiModelProperty(value = "档案编号", name = "travelNumber")
    private String travelNumber;

    /**
     *车长
     */
    @ApiModelProperty(value = "车长", name = "carLength")
    private String carLength;

    /**
     *车宽
     */
    @ApiModelProperty(value = "车宽", name = "carWidthc")
    private String carWidth;

    /**
     *车高
     */
    @ApiModelProperty(value = "车高", name = "carWidth")
    private String carHeight;

    /**
     *道路运输经营许可证号
     */
    @ApiModelProperty(value = "道路运输经营许可证号", name = "roadtranNumber")
    private String roadtranNumber;


    @ApiModelProperty(value = "网络货运主体id")
    private String networkMainBodyId;

    @ApiModelProperty(value = "实际承运人类型： 1:企业，2:个人")
    private String individualFlag;

    /**
     *道路运输许可证正页
     */
    @ApiModelProperty(value = "道路运输许可证正页", name = "qualificationNumberImage")
    private String qualificationNumberImage;

    /**
     * 数据添加标识【10-导入；20-非导入】
     */
    @ApiModelProperty(value = "添加源头", name = "dataAddTag")
    private String dataAddTag;

    /**
     * 上报人id
     */
    @ApiModelProperty(value = "上报人id", name = "reporterId")
    private String reporterId;

    /**
     * 轴数
     */
    @ApiModelProperty(value = "轴数", name = "axlenum")
    private String axlenum;

    /**
     * 实际承运人名称
     */
    @ApiModelProperty(value = "实际承运人名称", name = "actualCarrierName")
    private String actualCarrierName;

    /**
     * 实际承运人id(统一社会信用代码或个人证件号)
     */
    @ApiModelProperty(value = "实际承运人id", name = "actualCarrierId")
    private String actualCarrierId;

    /**
     * 车辆首次审核通过时间
     */
    @ApiModelProperty(value = "车辆首次审核通过时间", name = "firstApprovalTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstApprovalTime;

    /**
     * 车辆最新审核通过时间
     */
    @ApiModelProperty(value = "车辆最新审核通过时间", name = "lastestApprovalTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastestApprovalTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDataAddTag() {
        return dataAddTag;
    }

    public void setDataAddTag(String dataAddTag) {
        this.dataAddTag = dataAddTag;
    }

    public String getQualificationNumberImage() {
        return qualificationNumberImage;
    }

    public void setQualificationNumberImage(String qualificationNumberImage) {
        this.qualificationNumberImage = qualificationNumberImage;
    }

    public String getStatusType() {
        return statusType;
    }

    public void setStatusType(String statusType) {
        this.statusType = statusType;
    }

    public String getSuccessMessage() {
        return successMessage;
    }

    public void setSuccessMessage(String successMessage) {
        this.successMessage = successMessage;
    }

    @Override
    public Integer getReportNum() {
        return reportNum;
    }

    @Override
    public void setReportNum(Integer reportNum) {
        this.reportNum = reportNum;
    }

    public Integer getFinNum() {
        return finNum;
    }

    public void setFinNum(Integer finNum) {
        this.finNum = finNum;
    }

    @Override
    public String getNetworkMainBodyId() {
        return networkMainBodyId;
    }

    @Override
    public void setNetworkMainBodyId(String networkMainBodyId) {
        this.networkMainBodyId = networkMainBodyId;
    }

    public String getVehicleNumber() {
        return vehicleNumber;
    }

    public void setVehicleNumber(String vehicleNumber) {
        this.vehicleNumber = vehicleNumber;
    }

    public String getVehiclePlateColorCode() {
        return vehiclePlateColorCode;
    }

    public void setVehiclePlateColorCode(String vehiclePlateColorCode) {
        this.vehiclePlateColorCode = vehiclePlateColorCode;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getUseCharacter() {
        return useCharacter;
    }

    public void setUseCharacter(String useCharacter) {
        this.useCharacter = useCharacter;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getIssuingOrganizations() {
        return issuingOrganizations;
    }

    public void setIssuingOrganizations(String issuingOrganizations) {
        this.issuingOrganizations = issuingOrganizations;
    }

    public String getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(String registerDate) {
        this.registerDate = registerDate;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate;
    }

    public String getVehicleEnergyType() {
        return vehicleEnergyType;
    }

    public void setVehicleEnergyType(String vehicleEnergyType) {
        this.vehicleEnergyType = vehicleEnergyType;
    }

    public String getVehicleTonnage() {
        return vehicleTonnage;
    }

    public void setVehicleTonnage(String vehicleTonnage) {
        this.vehicleTonnage = vehicleTonnage;
    }

    public String getGrossMass() {
        return grossMass;
    }

    public void setGrossMass(String grossMass) {
        this.grossMass = grossMass;
    }

    public String getRoadTransportCertificateNumber() {
        return roadTransportCertificateNumber;
    }

    public void setRoadTransportCertificateNumber(String roadTransportCertificateNumber) {
        this.roadTransportCertificateNumber = roadTransportCertificateNumber;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReportMessage() {
        return reportMessage;
    }

    public void setReportMessage(String reportMessage) {
        this.reportMessage = reportMessage;
    }

    public String getResultMessage() {
        return resultMessage;
    }

    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getItem1() {
        return item1;
    }

    public void setItem1(String item1) {
        this.item1 = item1;
    }

    public String getItem2() {
        return item2;
    }

    public void setItem2(String item2) {
        this.item2 = item2;
    }

    public String getItem3() {
        return item3;
    }

    public void setItem3(String item3) {
        this.item3 = item3;
    }

    public String getItem4() {
        return item4;
    }

    public void setItem4(String item4) {
        this.item4 = item4;
    }

    public Date getSendToProDateTime() {
        return sendToProDateTime;
    }

    public void setSendToProDateTime(Date sendToProDateTime) {
        this.sendToProDateTime = sendToProDateTime;
    }

    public List<String> getVehicleIdList() {
        return vehicleIdList;
    }

    public void setVehicleIdList(List<String> vehicleIdList) {
        this.vehicleIdList = vehicleIdList;
    }

    public String getTrailerVehiclePlateNumber() {
        return trailerVehiclePlateNumber;
    }

    public void setTrailerVehiclePlateNumber(String trailerVehiclePlateNumber) {
        this.trailerVehiclePlateNumber = trailerVehiclePlateNumber;
    }

    public String getTrailerVehiclePlateColorCode() {
        return trailerVehiclePlateColorCode;
    }

    public void setTrailerVehiclePlateColorCode(String trailerVehiclePlateColorCode) {
        this.trailerVehiclePlateColorCode = trailerVehiclePlateColorCode;
    }

    public boolean isAgainReport() {
        return againReport;
    }

    public void setAgainReport(boolean againReport) {
        this.againReport = againReport;
    }

    public String getLicenseHomepage() {
        return licenseHomepage;
    }

    public void setLicenseHomepage(String licenseHomepage) {
        this.licenseHomepage = licenseHomepage;
    }

    public String getLicenseAuxiliaryPage() {
        return licenseAuxiliaryPage;
    }

    public void setLicenseAuxiliaryPage(String licenseAuxiliaryPage) {
        this.licenseAuxiliaryPage = licenseAuxiliaryPage;
    }

    public String getBusinessHouseholdsName() {
        return businessHouseholdsName;
    }

    public void setBusinessHouseholdsName(String businessHouseholdsName) {
        this.businessHouseholdsName = businessHouseholdsName;
    }

    public String getTrailerPlateNo() {
        return trailerPlateNo;
    }

    public void setTrailerPlateNo(String trailerPlateNo) {
        this.trailerPlateNo = trailerPlateNo;
    }

    public String getTravelNumber() {
        return travelNumber;
    }

    public void setTravelNumber(String travelNumber) {
        this.travelNumber = travelNumber;
    }

    public String getCarLength() {
        return carLength;
    }

    public void setCarLength(String carLength) {
        this.carLength = carLength;
    }

    public String getCarWidth() {
        return carWidth;
    }

    public void setCarWidth(String carWidth) {
        this.carWidth = carWidth;
    }

    public String getCarHeight() {
        return carHeight;
    }

    public void setCarHeight(String carHeight) {
        this.carHeight = carHeight;
    }

    public String getRoadtranNumber() {
        return roadtranNumber;
    }

    public void setRoadtranNumber(String roadtranNumber) {
        this.roadtranNumber = roadtranNumber;
    }

    public String getIndividualFlag() {
        return individualFlag;
    }

    public void setIndividualFlag(String individualFlag) {
        this.individualFlag = individualFlag;
    }

    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }

    public String getReporterId() {
        return reporterId;
    }

    public void setReporterId(String reporterId) {
        this.reporterId = reporterId;
    }
}
