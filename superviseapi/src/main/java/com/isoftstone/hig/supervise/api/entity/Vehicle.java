package com.isoftstone.hig.supervise.api.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(description = "主键id,车辆信息")
public class Vehicle  extends BaseRabbitMessageVO implements Serializable {

    private String id;

    //车牌号
    @ApiModelProperty(value = "车牌号", name = "vehicleNumber")
    private String vehicleNumber;

    //车辆颜色代码
    @ApiModelProperty(value = "车辆颜色代码", name = "vehiclePlateColorCode")
    private String vehiclePlateColorCode;

    //车辆类型
    @ApiModelProperty(value = "车辆类型", name = "vehicleType")
    private String vehicleType;

    //所有人
    @ApiModelProperty(value = "所有人", name = "owner")
    private String owner;

    //使用性质
    @ApiModelProperty(value = "使用性质", name = "useCharacter")
    private String useCharacter;

    //车辆识别代号
    @ApiModelProperty(value = "车辆识别代号", name = "VIN")
    private String vin;

    //发证机关
    @ApiModelProperty(value = "发证机关", name = "issuingOrganizations")
    private String issuingOrganizations;

    //注册日期
    @ApiModelProperty(value = "注册日期", name = "registerDate")
    private String registerDate;

    //发证日期
    @ApiModelProperty(value = "发证日期", name = "issueDate")
    private String issueDate;

    //车辆能源类型
    @ApiModelProperty(value = "车辆能源类型", name = "vehicleEnergyType")
    private String vehicleEnergyType;

    //核定载质量
    @ApiModelProperty(value = "核定载质量", name = "vehicleTonnage")
    private String vehicleTonnage;

    //吨位
    @ApiModelProperty(value = "吨位", name = "grossMass")
    private String grossMass;

    //道路运输证号
    @ApiModelProperty(value = "道路运输证号", name = "roadTransportCertificateNumber")
    private String roadTransportCertificateNumber;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "挂车牌照号", name = "trailerVehiclePlateNumber")
    private String trailerVehiclePlateNumber;

    @ApiModelProperty(value = "挂车牌照颜色", name = "trailerVehiclePlateColorCode")
    private String trailerVehiclePlateColorCode;

    @ApiModelProperty(value = "车辆类型【select:1-整车,2-车头,3-挂车】", name = "carType")
    private String carType;

    //状态：0：删除，1：待处理，2：已保存，3：待上报，4上报成功，5上报失败
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    //上报报文
    @ApiModelProperty(value = "上报报文", name = "reportMessage")
    private String reportMessage;

    //返回报文
    @ApiModelProperty(value = "返回报文", name = "resultMessage")
    private String resultMessage;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", name = "modifyBy")
    private String modifyBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "modifyDate")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyDate;

    /**
     * 上报时间
     */
    @ApiModelProperty(value = "上报时间", name = "sendToProDateTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendToProDateTime;

    /**
     *预留字段1
     */
    @ApiModelProperty(value = "预留字段1", name = "item1")
    private String item1;
    /**
     *预留字段2
     */
    @ApiModelProperty(value = "预留字段2", name = "item2")
    private String item2;
    /**
     *预留字段3
     */
    @ApiModelProperty(value = "预留字段3", name = "item3")
    private String item3;
    /**
     *预留字段4
     */
    @ApiModelProperty(value = "预留字段4", name = "item4")
    private String item4;

    @ApiModelProperty(value = "车辆Id集合", name = "vehicleIdList")
    private List<String> vehicleIdList;

    /**
     * true为是，false为否
     */
    @ApiModelProperty(value = "是否多次上传", name = "againReport")
    private boolean againReport = false;

    @ApiModelProperty(value = "本次上报数量", name = "reportNum")
    private Integer reportNum;

    @ApiModelProperty(value = "已完成上报数量", name = "finNum")
    private Integer finNum;

    @ApiModelProperty("返回报文")
    private String successMessage;


    @ApiModelProperty(value = "状态类型")
    private String statusType;
    /*-----------------------------------------小快新增字段----------------------------------------------*/

    /**
     *行驶证主页
     */
    @ApiModelProperty(value = "行驶证主页", name = "licenseHomepage")
    private String licenseHomepage;

    /**
     *行驶证副业
     */
    @ApiModelProperty(value = "行驶证副业", name = "licenseAuxiliaryPage")
    private String licenseAuxiliaryPage;

    /**
     *业主名称
     */
    @ApiModelProperty(value = "业主名称", name = "businessHouseholdsName")
    private String businessHouseholdsName;

    /**
     *挂车车牌号
     */
    @ApiModelProperty(value = "挂车车牌号", name = "trailerPlateNo")
    private String trailerPlateNo;

    /**
     *档案编号
     */
    @ApiModelProperty(value = "档案编号", name = "travelNumber")
    private String travelNumber;

    /**
     *车长
     */
    @ApiModelProperty(value = "车长", name = "carLength")
    private String carLength;

    /**
     *车宽
     */
    @ApiModelProperty(value = "车宽", name = "carWidthc")
    private String carWidth;

    /**
     *车高
     */
    @ApiModelProperty(value = "车高", name = "carWidth")
    private String carHeight;

    /**
     *道路运输经营许可证号
     */
    @ApiModelProperty(value = "道路运输经营许可证号", name = "roadtranNumber")
    private String roadtranNumber;


    @ApiModelProperty(value = "网络货运主体id")
    private String networkMainBodyId;

    @ApiModelProperty(value = "实际承运人类型： 1:企业，2:个人")
    private String individualFlag;

    /**
     *道路运输许可证正页
     */
    @ApiModelProperty(value = "道路运输许可证正页", name = "qualificationNumberImage")
    private String qualificationNumberImage;

    /**
     * 数据添加标识【10-导入；20-非导入】
     */
    @ApiModelProperty(value = "添加源头", name = "dataAddTag")
    private String dataAddTag;

    /**
     * 上报人id
     */
    @ApiModelProperty(value = "上报人id", name = "reporterId")
    private String reporterId;

    /**
     * 主体上报返回消息
     */
    @ApiModelProperty(value = "主体上报返回消息")
    private String resultInfo;

    /**
     * 网络货运主体名称
     */
    @ApiModelProperty(value = "网络货运主体名称")
    private String networkMainBodyName;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "主体数据上传时间", name = "messageTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date messageTime;

    @ApiModelProperty(value = "轴数", name = "axlenum")
    private String axlenum;

    @ApiModelProperty(value = "实际承运人名称", name = "actualCarrierName")
    private String actualCarrierName;

    @ApiModelProperty(value = "实际承运人id", name = "actualCarrierId")
    private String actualCarrierId;

    @ApiModelProperty(value = "车辆首次审核通过时间", name = "firstApprovalTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstApprovalTime;

    @ApiModelProperty(value = "车辆最新审核通过时间", name = "lastestApprovalTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastestApprovalTime;

}
