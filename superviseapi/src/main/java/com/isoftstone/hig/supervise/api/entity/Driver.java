package com.isoftstone.hig.supervise.api.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 司机
 *
 * <AUTHOR>
 * @date 2022/12/14 08:59:49
 */
@ApiModel(description = "主键id,司机信息")
@Data
public class Driver extends BaseRabbitMessageVO implements Serializable {

    private static final long serialVersionUID = 100000L;

    @ApiModelProperty(value = "司机id", name = "driverId")
    private String driverId;

    //姓名
    @ApiModelProperty(value = "姓名", name = "driverName")
    private String driverName;

    //身份证号
    @ApiModelProperty(value = "身份证号", name = "drivingLicense")
    private String drivingLicense;

    //准驾车型
    @ApiModelProperty(value = "准驾车型", name = "vehicleClass")
    private String vehicleClass;

    //驾驶证发证机关
    @ApiModelProperty(value = "驾驶证发证机关", name = "issuingOrganizations")
    private String issuingOrganizations;

    //驾驶证有效期自
    @ApiModelProperty(value = "驾驶证有效期自", name = "validPeriodFrom")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private String validPeriodFrom;

    //驾驶证有效期至
    @ApiModelProperty(value = "驾驶证有效期至", name = "validPeriodTo")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private String validPeriodTo;

    //从业资格证号
    @ApiModelProperty(value = "从业资格证号", name = "qualificationCertificate")
    private String qualificationCertificate;

    //手机号码
    @ApiModelProperty(value = "手机号码", name = "telephone")
    private String telephone;

    //状态：0：删除，1：待处理，2：已保存，3：待上报，4上报成功，5上报失败
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    //上报报文
    @ApiModelProperty(value = "上报报文", name = "reportMessage")
    private String reportMessage;

    //返回报文
    @ApiModelProperty(value = "返回报文", name = "resultMessage")
    private String resultMessage;

    @ApiModelProperty(value = "司机idList", name = "driverList")
    private List<String> driverList;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", name = "modifyBy")
    private String modifyBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "modifyDate")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date modifyDate;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间", name = "sendToProDateTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date sendToProDateTime;

    /**
     *预留字段1
     */
    @ApiModelProperty(value = "预留字段1", name = "item1")
    private String item1;
    /**
     *预留字段2
     */
    @ApiModelProperty(value = "预留字段2", name = "item2")
    private String item2;
    /**
     *预留字段3
     */
    @ApiModelProperty(value = "预留字段3", name = "item3")
    private String item3;
    /**
     *预留字段4
     */
    @ApiModelProperty(value = "预留字段4", name = "item4")
    private String item4;

    /**
     * true为是，false为否
     */
    @ApiModelProperty(value = "是否多次上传", name = "againReport")
    private boolean againReport = false;

    @ApiModelProperty(value = "上报数量", name = "reportNum")
    private Integer reportNum;

    @ApiModelProperty(value = "上报成功数量", name = "finNum")
    private Integer finNum;

    @ApiModelProperty("返回报文")
    private String successMessage;


    @ApiModelProperty(value = "状态类型")
    private String statusType;

    /*-----------------------------------------小快新增字段----------------------------------------------*/

    /**
     * 驾驶证图片地址
     */
    @ApiModelProperty(value = "驾驶证图片地址", name = "exDrivingForntFileUrl")
    private String exDrivingForntFileUrl;

    /**
     * 驾驶证号
     */
    @ApiModelProperty(value = "驾驶证号", name = "exDrivingLicenseNo")
    private String exDrivingLicenseNo;

    /**
     * 身份证正面地址
     */
    @ApiModelProperty(value = "身份证正面地址", name = "exIdCardForntFileUrl")
    private String exIdCardForntFileUrl;

    /**
     * 身份证背面地址
     */
    @ApiModelProperty(value = "身份证背面地址", name = "exIdCardBackFileUrl")
    private String exIdCardBackFileUrl;

    /**
     * 身份证的有效结束日期
     */
    @ApiModelProperty(value = "身份证的有效结束日期", name = "exLicenseEndDate")
    private Date exLicenseEndDate;

    /**
     * 身份证地址
     */
    @ApiModelProperty(value = "身份证地址", name = "exAddressDetail")
    private String exAddressDetail;

    /**
     * 从业资格证主页
     */
    @ApiModelProperty(value = "从业资格证主页代劳", name = "exCertificateForntFileUrl")
    private String exCertificateForntFileUrl;

    @ApiModelProperty(value = "网络货运主体id")
    private String networkMainBodyId;

    /**
     * 数据添加标识【10-导入；20-非导入】
     */
    @ApiModelProperty(value = "添加源头", name = "dataAddTag")
    private String dataAddTag;

    /**
     * 上报人id
     */
    @ApiModelProperty(value = "上报人id", name = "reporterId")
    private String reporterId;

    /**
     * 主体上报返回消息
     */
    @ApiModelProperty(value = "主体上报返回消息")
    private String resultInfo;

    /**
     * 网络货运主体名称
     */
    @ApiModelProperty(value = "网络货运主体名称")
    private String networkMainBodyName;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "主体数据上传时间", name = "messageTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date messageTime;

    @ApiModelProperty(value = "性别[1-男,2-女", name = "gender")
    private Integer gender;

    @ApiModelProperty(value = "司机首次审核通过时间", name = "firstApprovalTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date firstApprovalTime;

    @ApiModelProperty(value = "司机最新审核通过时间", name = "lastestApprovalTime")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastestApprovalTime;


    public String getDataAddTag() {
        return dataAddTag;
    }

    public void setDataAddTag(String dataAddTag) {
        this.dataAddTag = dataAddTag;
    }

    public String getStatusType() {
        return statusType;
    }

    public void setStatusType(String statusType) {
        this.statusType = statusType;
    }

    public String getSuccessMessage() {
        return successMessage;
    }

    public void setSuccessMessage(String successMessage) {
        this.successMessage = successMessage;
    }

    @Override
    public Integer getReportNum() {
        return reportNum;
    }

    @Override
    public void setReportNum(Integer reportNum) {
        this.reportNum = reportNum;
    }

    public Integer getFinNum() {
        return finNum;
    }

    public void setFinNum(Integer finNum) {
        this.finNum = finNum;
    }

    @Override
    public String getNetworkMainBodyId() {
        return networkMainBodyId;
    }

    @Override
    public void setNetworkMainBodyId(String networkMainBodyId) {
        this.networkMainBodyId = networkMainBodyId;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getDrivingLicense() {
        return drivingLicense;
    }

    public void setDrivingLicense(String drivingLicense) {
        this.drivingLicense = drivingLicense;
    }

    public String getVehicleClass() {
        return vehicleClass;
    }

    public void setVehicleClass(String vehicleClass) {
        this.vehicleClass = vehicleClass;
    }

    public String getIssuingOrganizations() {
        return issuingOrganizations;
    }

    public void setIssuingOrganizations(String issuingOrganizations) {
        this.issuingOrganizations = issuingOrganizations;
    }

    public String getValidPeriodFrom() {
        return validPeriodFrom;
    }

    public void setValidPeriodFrom(String validPeriodFrom) {
        this.validPeriodFrom = validPeriodFrom;
    }

    public String getValidPeriodTo() {
        return validPeriodTo;
    }

    public void setValidPeriodTo(String validPeriodTo) {
        this.validPeriodTo = validPeriodTo;
    }

    public String getQualificationCertificate() {
        return qualificationCertificate;
    }

    public void setQualificationCertificate(String qualificationCertificate) {
        this.qualificationCertificate = qualificationCertificate;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReportMessage() {
        return reportMessage;
    }

    public void setReportMessage(String reportMessage) {
        this.reportMessage = reportMessage;
    }

    public String getResultMessage() {
        return resultMessage;
    }

    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public Date getSendToProDateTime() {
        return sendToProDateTime;
    }

    public void setSendToProDateTime(Date sendToProDateTime) {
        this.sendToProDateTime = sendToProDateTime;
    }

    public String getDriverId() {
        return driverId;
    }

    public void setDriverId(String driverId) {
        this.driverId = driverId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getItem1() {
        return item1;
    }

    public void setItem1(String item1) {
        this.item1 = item1;
    }

    public String getItem2() {
        return item2;
    }

    public void setItem2(String item2) {
        this.item2 = item2;
    }

    public String getItem3() {
        return item3;
    }

    public void setItem3(String item3) {
        this.item3 = item3;
    }

    public String getItem4() {
        return item4;
    }

    public void setItem4(String item4) {
        this.item4 = item4;
    }

    public List<String> getDriverList() {
        return driverList;
    }

    public void setDriverList(List<String> driverList) {
        this.driverList = driverList;
    }

    public boolean isAgainReport() {
        return againReport;
    }

    public void setAgainReport(boolean againReport) {
        this.againReport = againReport;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getExDrivingForntFileUrl() {
        return exDrivingForntFileUrl;
    }

    public void setExDrivingForntFileUrl(String exDrivingForntFileUrl) {
        this.exDrivingForntFileUrl = exDrivingForntFileUrl;
    }

    public String getExDrivingLicenseNo() {
        return exDrivingLicenseNo;
    }

    public void setExDrivingLicenseNo(String exDrivingLicenseNo) {
        this.exDrivingLicenseNo = exDrivingLicenseNo;
    }

    public String getExIdCardForntFileUrl() {
        return exIdCardForntFileUrl;
    }

    public void setExIdCardForntFileUrl(String exIdCardForntFileUrl) {
        this.exIdCardForntFileUrl = exIdCardForntFileUrl;
    }

    public String getExIdCardBackFileUrl() {
        return exIdCardBackFileUrl;
    }

    public void setExIdCardBackFileUrl(String exIdCardBackFileUrl) {
        this.exIdCardBackFileUrl = exIdCardBackFileUrl;
    }

    public Date getExLicenseEndDate() {
        return exLicenseEndDate;
    }

    public void setExLicenseEndDate(Date exLicenseEndDate) {
        this.exLicenseEndDate = exLicenseEndDate;
    }

    public String getExAddressDetail() {
        return exAddressDetail;
    }

    public void setExAddressDetail(String exAddressDetail) {
        this.exAddressDetail = exAddressDetail;
    }

    public String getExCertificateForntFileUrl() {
        return exCertificateForntFileUrl;
    }

    public void setExCertificateForntFileUrl(String exCertificateForntFileUrl) {
        this.exCertificateForntFileUrl = exCertificateForntFileUrl;
    }

    public String getReporterId() {
        return reporterId;
    }

    public void setReporterId(String reporterId) {
        this.reporterId = reporterId;
    }
}
