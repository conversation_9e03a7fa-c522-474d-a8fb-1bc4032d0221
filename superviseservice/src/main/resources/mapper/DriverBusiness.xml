<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
/**=========================================================
*===       此类是由代码工具生成，框架开发者
*===       框架开发者Create By: 李健华
*===       @date: 2019/11/6 20:52:03
*=========================================================
*/
-->

<mapper namespace="com.isoftstone.hig.supervise.service.repository.DriverBusinessRepository">

    <sql id="Base_Column_List" >
	    driver_id, driver_name,	driving_license,	vehicle_class,	issuing_organizations,
	    valid_period_from,	 valid_period_to, qualification_certificate, telephone, send_to_pro_date_time, status,
	    remark,	create_by,	create_date, modify_by, modify_date, item1, item2, item3, item4,report_num,fin_num,success_message,data_add_tag,
	    gender,first_approval_time,lastest_approval_time
    </sql>

    <sql id="Driver_Column_List" >
        driver.driver_id,
        driver.driver_name,
        driver.driving_license,
        driver.vehicle_class,
        driver.issuing_organizations,
        driver.valid_period_from,
        driver.valid_period_to,
        driver.qualification_certificate,
        driver.telephone,
        driver.send_to_pro_date_time,
        driver.status,
        driver.remark,
        driver.create_by,
        driver.create_date,
        driver.modify_by,
        driver.modify_date,
        driver.item1,
        driver.item2,
        driver.item3,
        driver.item4,
        driver.report_num,
        driver.fin_num,
        driver.success_message,
        driver.data_add_tag
    </sql>

    <insert id="addDriverBusinessInfo" parameterType="com.isoftstone.hig.supervise.api.entity.Driver">
        INSERT INTO
        supervise_driver_bus_info(
            driver_id, driver_name,	driving_license,	vehicle_class,	issuing_organizations,
            valid_period_from,	 valid_period_to, qualification_certificate, telephone, send_to_pro_date_time, status,
            remark,	create_by,	create_date, modify_by, modify_date, item1, item2, item3, item4,report_num,fin_num,data_add_tag,
            gender,first_approval_time,lastest_approval_time
	   )
        VALUES
        (#{driverId,jdbcType=VARCHAR},
         #{driverName,jdbcType=VARCHAR},
         #{drivingLicense,jdbcType=VARCHAR},
         #{vehicleClass,jdbcType=VARCHAR},
         #{issuingOrganizations,jdbcType=VARCHAR},
         #{validPeriodFrom,jdbcType=TIMESTAMP},
         #{validPeriodTo,jdbcType=TIMESTAMP},
         #{qualificationCertificate,jdbcType=VARCHAR},
         #{telephone,jdbcType=VARCHAR},
         #{sendToProDateTime,jdbcType=TIMESTAMP},
         #{status,jdbcType=CHAR},
         #{remark,jdbcType=VARCHAR},
         #{createBy,jdbcType=VARCHAR},
         NOW(),
         #{modifyBy,jdbcType=VARCHAR},
         #{modifyDate,jdbcType=TIMESTAMP},
         #{item1,jdbcType=VARCHAR},
         #{item2,jdbcType=VARCHAR},
         #{item3,jdbcType=VARCHAR},
         #{item4,jdbcType=VARCHAR},
         0,0,#{dataAddTag,jdbcType=VARCHAR},
         #{gender,jdbcType=INTEGER},
         #{firstApprovalTime,jdbcType=TIMESTAMP},
         #{lastestApprovalTime,jdbcType=TIMESTAMP}
         )
    </insert>

    <sql id="list_condition" >
        <if test="driverName !=null and driverName != ''   " >
            and driver.driver_name = #{driverName}
        </if>
        <if test="drivingLicense !=null and drivingLicense != ''   " >
            and driver.driving_license = #{drivingLicense}
        </if>
        <if test="telephone !=null and telephone != ''   " >
            and driver.telephone like CONCAT('%',#{telephone},'%' )
        </if>
        <if test="validReportStart != null and validReportStart != ''  ">
            AND driver.send_to_pro_date_time >= #{validReportStart}
        </if>
        <if test="validReportEnd != null and validReportEnd !=''  ">
            <![CDATA[ AND driver.send_to_pro_date_time <= #{validReportEnd} ]]>
        </if>
        <if test="status !=null and status != ''   " >
            and driver.status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="dataAddTag !=null and dataAddTag != ''   " >
            and driver.data_add_tag = #{dataAddTag,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="queryDriverBusInfoBySearchCondition" parameterType="com.isoftstone.hig.supervise.api.filter.DriverBusSearchVo" resultType="com.isoftstone.hig.supervise.api.entity.Driver">
    	select
        <include refid="Base_Column_List" />
    	from supervise_driver_bus_info driver
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <include refid="list_condition"/>
        </trim>
		order by create_date desc
    </select>

    <select id="countDriverBusInfoByNetworkIdCondition" parameterType="com.isoftstone.hig.supervise.api.filter.DriverBusSearchVo" resultType="int">
        select count(0)
        from supervise_driver_bus_info driver
        inner join supervise_message_note m on driver.driver_id = m.message_content_id
        where m.message_type ='driver'
        and m.message_status = '1'
        and m.network_main_body_id = #{networkMainBodyId}
        <if test="netValidReportStart != null and netValidReportStart != ''  ">
            AND m.message_time >= #{netValidReportStart}
        </if>
        <if test="netValidReportEnd != null and netValidReportEnd !=''  ">
            <![CDATA[ AND m.message_time <= #{netValidReportEnd} ]]>
        </if>
        <include refid="list_condition"/>
    </select>

    <select id="queryDriverBusInfoByNetworkIdCondition" parameterType="com.isoftstone.hig.supervise.api.filter.DriverBusSearchVo" resultType="com.isoftstone.hig.supervise.api.entity.Driver">
        select
        <include refid="Driver_Column_List" />
        ,m.result_info
        ,m.network_main_body_name
        ,m.message_time
        from supervise_driver_bus_info driver
        inner join supervise_message_note m on driver.driver_id = m.message_content_id
        <where>
            m.message_type ='driver'
            and m.message_status  = '1'
            and m.network_main_body_id = #{networkMainBodyId}
            <if test="netValidReportStart != null and netValidReportStart != ''  ">
                AND m.message_time >= #{netValidReportStart}
            </if>
            <if test="netValidReportEnd != null and netValidReportEnd !=''  ">
                <![CDATA[ AND m.message_time <= #{netValidReportEnd} ]]>
            </if>
            <include refid="list_condition"/>
        </where>
        order by create_date desc
    </select>

    <select id="exportDriver" parameterType="com.isoftstone.hig.common.model.PagingInfo" resultType="com.isoftstone.hig.supervise.api.entity.Driver">
        select
        <include refid="Base_Column_List" />
        from supervise_driver_bus_info
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="driverName !=null and driverName != ''   " >
                and driver_name = #{driverName}
            </if>
            <if test="drivingLicense !=null and drivingLicense != ''   " >
                and driving_license = #{drivingLicense}
            </if>
            <if test="telephone !=null and telephone != ''   " >
                and telephone like CONCAT('%',#{telephone},'%' )
            </if>
            <if test="validReportStart != null and validReportStart != ''  ">
                AND send_to_pro_date_time >= #{validReportStart}
            </if>
            <if test="validReportEnd != null and validReportEnd !=''  ">
                <![CDATA[ AND send_to_pro_date_time <= #{validReportEnd} ]]>
            </if>
            <if test="status !=null and status != ''   " >
                and status = #{status,jdbcType=VARCHAR}
            </if>
        </trim>
        order by create_date desc
    </select>

    <select id="viewDriverBusInfo" parameterType="com.isoftstone.hig.supervise.api.entity.Driver" resultType="com.isoftstone.hig.supervise.api.entity.Driver">
    	select
        <include refid="Base_Column_List" />
    	from supervise_driver_bus_info
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="driverId != null  and driverId != '' " >
		         and driver_id = #{driverId,jdbcType=VARCHAR}
            </if>
            <if test="drivingLicense != null  and drivingLicense != '' " >
                and driving_license = #{drivingLicense}
            </if>
        </trim>
    </select>

    <select id="searchExistsData" parameterType="com.isoftstone.hig.supervise.api.filter.DriverBusSearchVo"  resultType="com.isoftstone.hig.supervise.api.entity.Driver">
    	select
    		driver_id,
			driver_name,
    		status
    	from supervise_driver_bus_info
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="idList != null  and idList.size() >0 " >
                <foreach collection="idList" item="id" open="and driver_id in("  separator=","  close=")">
		            #{id,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="statusList != null  and statusList.size() > 0" >
                <foreach collection="statusList" item="status" open="and status not in("  separator=","  close=")">
		            #{status,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </select>

    <update id="updateDriverBusInfo" parameterType="com.isoftstone.hig.supervise.api.entity.Driver" >
    update supervise_driver_bus_info
        <set >
            <if test="vehicleClass != null " >
        vehicle_class = #{vehicleClass,jdbcType=VARCHAR},
            </if>
            <if test="issuingOrganizations != null " >
        issuing_organizations = #{issuingOrganizations,jdbcType=VARCHAR},
            </if>
            <if test="validPeriodFrom != null " >
        valid_period_from = #{validPeriodFrom,jdbcType=VARCHAR},
            </if>
            <if test="validPeriodTo != null " >
        valid_period_to = #{validPeriodTo,jdbcType=VARCHAR},
            </if>
            <if test="qualificationCertificate != null " >
        qualification_certificate = #{qualificationCertificate,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null  " >
        telephone = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="status != null  " >
        status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null  " >
        remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null " >
        modify_date = NOW(),
            </if>
            <if test="modifyBy != null " >
                modify_by = #{modifyBy,jdbcType=VARCHAR},
            </if>
            <if test="sendToProDateTime != null ">
          send_to_pro_date_time = #{sendToProDateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="driverName != null " >
		  driver_name = #{driverName,jdbcType=VARCHAR},
            </if>
            <if test="gender != null " >
                gender = #{gender,jdbcType=INTEGER},
            </if>
            <if test="firstApprovalTime != null " >
                first_approval_time = #{firstApprovalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastestApprovalTime != null " >
                lastest_approval_time = #{lastestApprovalTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="driverId != null  and driverId != '' " >
	         and driver_id = #{driverId,jdbcType=VARCHAR}
            </if>
            <if test="drivingLicense != null  and drivingLicense != '' " >
                and driving_license = #{drivingLicense}
            </if>
        </trim>
    </update>


    <update id="updateReportStatusOfCompleted"  parameterType="com.isoftstone.hig.supervise.api.entity.Driver" >
        update supervise_driver_bus_info
        set status = #{status},send_to_pro_date_time = #{sendToProDateTime},modify_date= #{modifyDate},fin_num=#{finNum},
            success_message=#{successMessage}
        where driver_id = #{driverId}
    </update>

    <update id="updateReportingStatus"  parameterType="com.isoftstone.hig.supervise.api.entity.Driver" >
        update supervise_driver_bus_info
        set status = #{status},report_num = #{reportNum},fin_num = #{finNum},modify_date= #{modifyDate}
        <if test="modifyBy != null " >
            ,modify_by = #{modifyBy,jdbcType=VARCHAR}
        </if>
        <if test="reporterId != null " >
            ,reporter_id = #{reporterId,jdbcType=VARCHAR}
        </if>
        where driver_id = #{driverId}
    </update>


    <select id="queryReportNumById" parameterType="com.isoftstone.hig.supervise.api.entity.Driver" resultType="com.isoftstone.hig.supervise.api.entity.Driver">
        select driver_id,report_num,fin_num,status  from supervise_driver_bus_info
        where driver_id = #{driverId}
    </select>

    <select id="queryReportDriverData" parameterType="com.isoftstone.hig.supervise.api.entity.Driver" resultType="com.isoftstone.hig.supervise.api.entity.Driver">
        select
        <include refid="Base_Column_List" />
        from supervise_driver_bus_info where status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
        order by create_date desc,driver_id
        limit #{start},#{end}
    </select>

    <select id="countReportDriver" parameterType="com.isoftstone.hig.supervise.api.entity.Driver" resultType="java.lang.Integer">
        select count(*) from supervise_driver_bus_info where status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
    </select>


    <select id="queryDriverByDrivingLicense" parameterType="com.isoftstone.hig.supervise.api.entity.Driver" resultType="com.isoftstone.hig.supervise.api.entity.Driver">
        select
        <include refid="Base_Column_List" />
        from supervise_driver_bus_info where driving_license in
        <foreach collection="drivingLicenseList" item="drivingLicense" open="(" close=")" separator=",">
            #{drivingLicense}
        </foreach>
    </select>

    <select id="autoQueryReportDriver" parameterType="com.isoftstone.hig.supervise.api.entity.Driver" resultType="com.isoftstone.hig.supervise.api.entity.Driver">
        select
        <include refid="Base_Column_List" />
        from supervise_driver_bus_info  a
        where a.status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
        and NOT EXISTS (
        SELECT 1
        FROM supervise_message_note b
        WHERE a.driver_id = b.message_content_id AND b.message_type = 'driver' AND b.message_status = '1' AND b.network_main_body_id = #{networkMainBodyId}
        ) order by a.create_date  desc
        limit #{pageSize}
    </select>

    <select id="queryDriverReport" resultType="com.isoftstone.hig.supervise.api.entity.Driver">
        SELECT
            b.driver_id,
            b.driver_name,
            b.driving_license
        FROM
            supervise_message_note m,
            supervise_driver_bus_info b
        WHERE
            m.message_content_id = b.driver_id
        <foreach collection="drivingLicenseList" item="drivingLicense" open="and b.driving_license in (" close=")" separator=",">
                #{drivingLicense}
        </foreach>
          AND m.message_type = 'driver'
          AND m.network_main_body_id = #{networkMainBodyId}
          AND m.message_status in ('1','2')
    </select>

</mapper>
