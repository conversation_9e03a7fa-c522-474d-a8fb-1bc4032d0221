<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
/**=========================================================
*===       此类是由代码工具生成，框架开发者
*===       框架开发者Create By: 李健华
*===       @date: 2019/11/6 20:52:03
*=========================================================
*/
-->

<mapper namespace="com.isoftstone.hig.supervise.service.repository.VehicleBusinessRepository">

    <sql id="Base_Column_List" >
	   	  id,vehicle_number,vehicle_plate_color_code, vehicle_type, owner,
		  use_character, vin, issuing_organizations, register_date, issue_date, vehicle_energy_type, vehicle_tonnage,
		  gross_mass, road_transport_certificate_number, send_to_pro_date_time, status, remark, trailer_vehicle_plate_number,
		  create_by, create_date, modify_by, modify_date, item1, item2, item3, item4,report_num,fin_num,success_message,
	   	  trailer_vehicle_plate_color_code, car_type ,data_add_tag ,individual_flag,
	   	  car_length,car_width,car_height,axlenum,actual_carrier_name,actual_carrier_id,first_approval_time,lastest_approval_time
    </sql>

    <sql id="Vehicle_Column_List" >
        vehicle.id,
        vehicle.vehicle_number,
        vehicle.vehicle_plate_color_code,
        vehicle.vehicle_type,
        vehicle.owner,
        vehicle.use_character,
        vehicle.vin,
        vehicle.issuing_organizations,
        vehicle.register_date,
        vehicle.issue_date,
        vehicle.vehicle_energy_type,
        vehicle.vehicle_tonnage,
        vehicle.gross_mass,
        vehicle.road_transport_certificate_number,
        vehicle.send_to_pro_date_time,
        vehicle.status,
        vehicle.remark,
        vehicle.trailer_vehicle_plate_number,
        vehicle.create_by,
        vehicle.create_date,
        vehicle.modify_by,
        vehicle.modify_date,
        vehicle.item1,
        vehicle.item2,
        vehicle.item3,
        vehicle.item4,
        vehicle.report_num,
        vehicle.fin_num,
        vehicle.success_message,
        vehicle.trailer_vehicle_plate_color_code,
        vehicle.car_type,
        vehicle.data_add_tag,
        vehicle.individual_flag
    </sql>

    <insert id="addVehicleInfo" parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        INSERT INTO
        supervise_vehicle_bus_info(
          id,
		  vehicle_number,
		  vehicle_plate_color_code,
		  vehicle_type,
		  owner,
		  use_character,
		  vin,
		  issuing_organizations,
		  register_date,
		  issue_date,
		  vehicle_energy_type,
		  vehicle_tonnage,
		  gross_mass,
		  road_transport_certificate_number,
		  status,
		  remark,
		  create_by,
		  create_date,
		  modify_by,
		  modify_date,
          item1,
          item2,
          item3,
          item4,
          report_num,
          fin_num,
          trailer_vehicle_plate_number,
          trailer_vehicle_plate_color_code,
          car_type,
          data_add_tag,
          individual_flag,
          car_length,car_width,car_height,axlenum,actual_carrier_name,actual_carrier_id,first_approval_time,lastest_approval_time
	   )
        VALUES
        (
         #{id},
         #{vehicleNumber,jdbcType=VARCHAR},
         #{vehiclePlateColorCode,jdbcType=VARCHAR},
         #{vehicleType,jdbcType=VARCHAR},
         #{owner,jdbcType=VARCHAR},
         #{useCharacter,jdbcType=VARCHAR},
         #{vin,jdbcType=VARCHAR},
         #{issuingOrganizations,jdbcType=VARCHAR},
         #{registerDate,jdbcType=VARCHAR},
         #{issueDate,jdbcType=VARCHAR},
         #{vehicleEnergyType,jdbcType=VARCHAR},
         #{vehicleTonnage,jdbcType=VARCHAR},
         #{grossMass,jdbcType=VARCHAR},
         #{roadTransportCertificateNumber,jdbcType=VARCHAR},
         #{status,jdbcType=CHAR},
         #{remark,jdbcType=VARCHAR},
         #{createBy,jdbcType=VARCHAR},
         NOW(),
         #{modifyBy,jdbcType=VARCHAR},
         #{modifyDate,jdbcType=TIMESTAMP},
         #{item1,jdbcType=VARCHAR},
         #{item2,jdbcType=VARCHAR},
         #{item3,jdbcType=VARCHAR},
         #{item4,jdbcType=VARCHAR},
         0,
         0,
         #{trailerVehiclePlateNumber,jdbcType=CHAR},
         #{trailerVehiclePlateColorCode,jdbcType=VARCHAR},
         #{carType,jdbcType=VARCHAR},
         #{dataAddTag,jdbcType=VARCHAR},
         #{individualFlag,jdbcType=VARCHAR},
         #{carLength},
         #{carWidth},
         #{carHeight},
         #{axlenum,jdbcType=VARCHAR},
         #{actualCarrierName,jdbcType=VARCHAR},
         #{actualCarrierId,jdbcType=VARCHAR},
         #{firstApprovalTime,jdbcType=TIMESTAMP},
         #{lastestApprovalTime,jdbcType=TIMESTAMP}
         )
    </insert>

    <sql id="list_condition" >
        <if test="vehicleNumber !=null and vehicleNumber != ''   " >
            and vehicle.vehicle_number = #{vehicleNumber}
        </if>
        <if test="owner !=null and owner != ''   " >
            and vehicle.owner like CONCAT('%',#{owner},'%' )
        </if>
        <if test="sendToProDateTimeFrom != null and sendToProDateTimeFrom != ''  ">
            AND vehicle.send_to_pro_date_time >= #{sendToProDateTimeFrom}
        </if>
        <if test="sendToProDateTimeTo != null and sendToProDateTimeTo !=''  ">
            <![CDATA[ AND vehicle.send_to_pro_date_time <= #{sendToProDateTimeTo} ]]>
        </if>
        <if test="status !=null and status != ''   " >
            and vehicle.status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="vehiclePlateColorCode !=null and vehiclePlateColorCode != ''   " >
            and vehicle.vehicle_plate_color_code = #{vehiclePlateColorCode,jdbcType=VARCHAR}
        </if>
        <if test="vehicleType !=null and vehicleType != ''   " >
            and vehicle.vehicle_type = #{vehicleType,jdbcType=VARCHAR}
        </if>
        <if test="carType !=null and carType != ''   " >
            and vehicle.car_type = #{carType,jdbcType=VARCHAR}
        </if>
        <if test="trailerVehiclePlateNumber != null  and trailerVehiclePlateNumber != '' " >
            and vehicle.trailer_vehicle_plate_number = #{trailerVehiclePlateNumber,jdbcType=VARCHAR}
        </if>
        <if test="dataAddTag !=null and dataAddTag != ''   " >
            and vehicle.data_add_tag = #{dataAddTag,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="queryVehicleBusInfoBySearchCondition" parameterType="com.isoftstone.hig.supervise.api.filter.VehicleBusSearchVo" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
    	select
        <include refid="Base_Column_List" />
    	from supervise_vehicle_bus_info vehicle
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <include refid="list_condition"/>
        </trim>
		order by create_date desc
    </select>

    <select id="countVehicleBusInfoByNetworkIdCondition" parameterType="com.isoftstone.hig.supervise.api.filter.VehicleBusSearchVo" resultType="int">
        select count(0)
        from supervise_vehicle_bus_info vehicle
        inner join supervise_message_note m on vehicle.id = m.message_content_id
        where m.message_type ='vehicle'
        and m.message_status = '1'
        and m.network_main_body_id = #{networkMainBodyId}
        <if test="netValidReportStart != null and netValidReportStart != ''  ">
            AND m.message_time >= #{netValidReportStart}
        </if>
        <if test="netValidReportEnd != null and netValidReportEnd !=''  ">
            <![CDATA[ AND m.message_time <= #{netValidReportEnd} ]]>
        </if>
        <include refid="list_condition"/>
    </select>

    <select id="queryVehicleBusInfoByNetworkIdCondition" parameterType="com.isoftstone.hig.supervise.api.filter.VehicleBusSearchVo" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        select
        <include refid="Vehicle_Column_List" />
        ,m.result_info
        ,m.network_main_body_name
        ,m.message_time
        from supervise_vehicle_bus_info vehicle
        inner join supervise_message_note m on vehicle.id = m.message_content_id
        <where>
            m.message_type ='vehicle'
            and m.message_status  = '1'
            and m.network_main_body_id = #{networkMainBodyId}
            <if test="netValidReportStart != null and netValidReportStart != ''  ">
                AND m.message_time >= #{netValidReportStart}
            </if>
            <if test="netValidReportEnd != null and netValidReportEnd !=''  ">
                <![CDATA[ AND m.message_time <= #{netValidReportEnd} ]]>
            </if>
            <include refid="list_condition"/>
        </where>
        order by create_date desc
    </select>

    <select id="exportVehicle" parameterType="com.isoftstone.hig.common.model.PagingInfo" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        select
        <include refid="Base_Column_List" />
        from supervise_vehicle_bus_info
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="vehicleNumber !=null and vehicleNumber != ''   " >
                and vehicle_number = #{vehicleNumber}
            </if>
            <if test="owner !=null and owner != ''   " >
                and owner like CONCAT('%',#{owner},'%' )
            </if>
            <if test="sendToProDateTimeFrom != null and sendToProDateTimeFrom != ''  ">
                AND send_to_pro_date_time >= #{sendToProDateTimeFrom}
            </if>
            <if test="sendToProDateTimeTo != null and sendToProDateTimeTo !=''  ">
                <![CDATA[ AND send_to_pro_date_time <= #{sendToProDateTimeTo} ]]>
            </if>
            <if test="status !=null and status != ''   " >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="vehiclePlateColorCode !=null and vehiclePlateColorCode != ''   " >
                and vehicle_plate_color_code = #{vehiclePlateColorCode,jdbcType=VARCHAR}
            </if>
            <if test="vehicleType !=null and vehicleType != ''   " >
                and vehicle_type = #{vehicleType,jdbcType=VARCHAR}
            </if>
            <if test="carType !=null and carType != ''   " >
                and car_type = #{carType,jdbcType=VARCHAR}
            </if>
            <if test="id !=null" >
                and id = #{id}
            </if>
        </trim>
        order by create_date desc
    </select>

    <select id="queryVehicleBusInfoById" parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        select
        <include refid="Base_Column_List" />
        from supervise_vehicle_bus_info where id = #{id}
    </select>
    <select id="queryVehicleBusInfo" parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
    	select
        <include refid="Base_Column_List" />
    	from supervise_vehicle_bus_info
        <where>
            status != '0'
            <if test="vehicleNumber != null  and vehicleNumber != '' " >
	         and vehicle_number = #{vehicleNumber,jdbcType=VARCHAR}
            </if>
            <if test="vehiclePlateColorCode != null  and vehiclePlateColorCode != '' " >
                and vehicle_plate_color_code = #{vehiclePlateColorCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="searchExistsData" parameterType="com.isoftstone.hig.supervise.api.filter.VehicleBusSearchVo" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
    	select
    		vehicle_number,status
    	from supervise_vehicle_bus_info
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="idList != null  and idList.size() >0 " >
                <foreach collection="idList" item="id" open="and id in("  separator=","  close=")">
		            #{id,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="statusList != null  and statusList.size() > 0" >
                <foreach collection="statusList" item="status" open="and status not in("  separator=","  close=")">
		            #{status,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </select>

    <update id="update" parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        update supervise_vehicle_bus_info
        <set >
            <if test="vehiclePlateColorCode != null and vehiclePlateColorCode != ''  " >
                vehicle_plate_color_code = #{vehiclePlateColorCode,jdbcType=VARCHAR},
            </if>
            <if test="vehicleType != null and vehicleType != ''  " >
                vehicle_type = #{vehicleType,jdbcType=VARCHAR},
            </if>
            <if test="owner != null and owner != '' " >
                owner = #{owner,jdbcType=VARCHAR},
            </if>
            <if test="useCharacter != null and useCharacter != '' " >
                use_character = #{useCharacter,jdbcType=VARCHAR},
            </if>
            <if test="vin != null and vin != '' " >
                vin = #{vin,jdbcType=VARCHAR},
            </if>
            <if test="issuingOrganizations != null and issuingOrganizations != '' " >
                issuing_organizations = #{issuingOrganizations,jdbcType=VARCHAR},
            </if>
            <if test="registerDate != null and registerDate != '' " >
                register_date = #{registerDate,jdbcType=VARCHAR},
            </if>
            <if test="issueDate != null and issueDate != '' " >
                issue_date = #{issueDate,jdbcType=VARCHAR},
            </if>
            <if test="vehicleEnergyType != null and vehicleEnergyType != '' " >
                vehicle_energy_type = #{vehicleEnergyType,jdbcType=VARCHAR},
            </if>
            <if test="vehicleTonnage != null and vehicleTonnage != '' " >
                vehicle_tonnage = #{vehicleTonnage,jdbcType=VARCHAR},
            </if>
            <if test="grossMass != null and grossMass != '' " >
                gross_mass = #{grossMass,jdbcType=VARCHAR},
            </if>
            <if test="roadTransportCertificateNumber != null and roadTransportCertificateNumber != '' " >
                road_transport_certificate_number = #{roadTransportCertificateNumber,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' " >
                status = #{status,jdbcType=CHAR},
            </if>
            <if test="trailerVehiclePlateNumber != null and trailerVehiclePlateNumber != '' " >
                trailer_vehicle_plate_number = #{trailerVehiclePlateNumber,jdbcType=VARCHAR},
            </if>
            <if test="remark != null   " >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="modifyBy != null " >
                modify_by = #{modifyBy,jdbcType=VARCHAR},
            </if>
            <if test="modifyDate != null " >
                modify_date = NOW(),
            </if>
            <if test="sendToProDateTime != null ">
		        send_to_pro_date_time = #{sendToProDateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="trailerVehiclePlateColorCode != null and trailerVehiclePlateColorCode != '' " >
                trailer_vehicle_plate_color_code = #{trailerVehiclePlateColorCode,jdbcType=VARCHAR},
            </if>
            <if test="carType != null and carType != '' " >
                car_type = #{carType,jdbcType=VARCHAR},
            </if>
            <if test="individualFlag != null and individualFlag != '' " >
                individual_flag = #{individualFlag,jdbcType=VARCHAR},
            </if>
            <if test="carLength != null and carLength != '' " >
                car_length = #{carLength},
            </if>
            <if test="carWidth != null and carWidth != '' " >
                car_width = #{carWidth},
            </if>
            <if test="carHeight != null and carHeight != '' " >
                car_height = #{carHeight},
            </if>
            <if test="axlenum != null and axlenum != '' " >
                axlenum = #{axlenum,jdbcType=VARCHAR},
            </if>
            <if test="actualCarrierName != null and actualCarrierName != '' " >
                actual_carrier_name = #{actualCarrierName,jdbcType=VARCHAR},
            </if>
            <if test="actualCarrierId != null and actualCarrierId != '' " >
                actual_carrier_id = #{actualCarrierId,jdbcType=VARCHAR},
            </if>
            <if test="firstApprovalTime != null">
                first_approval_time = #{firstApprovalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastestApprovalTime != null">
                lastest_approval_time = #{lastestApprovalTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateReportStatusOfCompleted"  parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle" >
        update supervise_vehicle_bus_info
        set status = #{status},send_to_pro_date_time = #{sendToProDateTime},modify_date= #{modifyDate},fin_num=#{finNum},
            success_message=#{successMessage}
        where id = #{id}
    </update>


    <update id="updateReportingStatus"  parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle" >
        update supervise_vehicle_bus_info
        set status = #{status},report_num = #{reportNum},fin_num = #{finNum},modify_date= #{modifyDate}
        <if test="modifyBy != null " >
            ,modify_by = #{modifyBy,jdbcType=VARCHAR}
        </if>
        <if test="reporterId != null " >
            ,reporter_id = #{reporterId,jdbcType=VARCHAR}
        </if>
        where id = #{id}
    </update>

    <select id="queryByNumberColor" parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        select
        <include refid="Base_Column_List" />
        from supervise_vehicle_bus_info
        where vehicle_number = #{vehicleNumber} and vehicle_plate_color_code = #{busVehicleColorCode} and status !='0'
    </select>

    <select id="queryReportVehicle" parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        select
        <include refid="Base_Column_List" />
        from supervise_vehicle_bus_info where status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
        order by create_date desc,vehicle_number
        limit #{start},#{end}
    </select>

    <select id="autoQueryReportVehicle" parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle"
            resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        select
        <include refid="Base_Column_List"/>
        from supervise_vehicle_bus_info a
        where a.status in
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
        and NOT EXISTS (
        SELECT 1
        FROM supervise_message_note b
        WHERE CONCAT(a.id ,'') = b.message_content_id AND b.message_type = 'vehicle' AND b.message_status = '1' AND b.network_main_body_id = #{networkMainBodyId}
        ) order by a.create_date  desc  limit #{pageSize}
    </select>


    <select id="queryVehicle" parameterType="com.isoftstone.hig.supervise.api.entity.Vehicle"
            resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        select
        <include refid="Base_Column_List"/>
        from supervise_vehicle_bus_info
        <where>
            <if test="vehicleNumber != null  and vehicleNumber != '' ">
                and vehicle_number = #{vehicleNumber,jdbcType=VARCHAR}
            </if>
            <if test="vehiclePlateColorCode != null  and vehiclePlateColorCode != '' ">
                and vehicle_plate_color_code = #{vehiclePlateColorCode,jdbcType=VARCHAR}
            </if>
            <if test="status != null  and status != '' ">
                and status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_date desc
    </select>

    <select id="queryVehicleReport" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        SELECT
            b.id,
            b.vehicle_number,
            b.vehicle_plate_color_code,
            b.vehicle_type,
            b.trailer_vehicle_plate_number,
            b.trailer_vehicle_plate_color_code,
            b.car_type
        FROM
        supervise_message_note m,
        supervise_vehicle_bus_info b
        WHERE
        m.message_content_id = CONCAT(b.id ,'')
        <foreach collection="vehicleNumberList" item="vehicleNumber" open="and b.vehicle_number in (" close=")" separator=",">
            #{vehicleNumber}
        </foreach>
        AND m.message_type = 'vehicle'
        AND m.network_main_body_id = #{networkMainBodyId}
        AND m.message_status in ('1','2')
    </select>

    <select id="queryVehicleByNumberList" resultType="com.isoftstone.hig.supervise.api.entity.Vehicle">
        select
        <include refid="Base_Column_List"/>
        from supervise_vehicle_bus_info
        <where>
            status != '0'
            and vehicle_number in
            <foreach collection="vehicleNumberList" item="vehicleNumber" open="(" close=")" separator=",">
                #{vehicleNumber}
            </foreach>
        </where>
    </select>


    <update id="updateVehicleActualCarrier">
        update supervise_vehicle_bus_info set actual_carrier_name = #{actualCarrierName},actual_carrier_id = #{actualCarrierId}
        where vehicle_number = #{vehicleNumber}
    </update>

</mapper>
