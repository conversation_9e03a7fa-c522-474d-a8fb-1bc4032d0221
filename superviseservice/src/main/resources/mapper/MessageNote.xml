<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.isoftstone.hig.supervise.service.repository.MessageNoteRepository" >

    <insert id="insertMessageNote" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote">
        insert into supervise_message_note (
            message_id, message_type, message_content_id,message_time, message_status, remark,create_by, create_date, modify_by,modify_date,
             item1,item2, item3, item4, network_main_body_id,network_main_body_name,result_info)
        values (
            #{messageId},#{messageType},#{messageContentId},#{messageTime},#{messageStatus},#{remark},#{createBy},#{createDate},#{modifyBy},#{modifyDate},
            #{item1},#{item2},#{item3},#{item4},
            #{networkMainBodyId},#{networkMainBodyName},#{resultInfo}
          )
    </insert>

    <update id="deleteMessageNoteByMessageId" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote">
        delete from supervise_message_note where message_content_id=#{messageContentId} and message_type=#{messageType} and network_main_body_id=#{networkMainBodyId}
    </update>

    <update id="deleteMessageNoteByMessageContentId" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote">
        delete from supervise_message_note where message_content_id=#{messageContentId} and message_type=#{messageType}
    </update>

    <select id="queryMessagePageInfo" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote" resultType="com.isoftstone.hig.supervise.api.entity.MessageNote">
        select
        message_content_id,message_time,message_type, message_status,network_main_body_id,network_main_body_name,result_info
        from supervise_message_note
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="messageType !=null and messageType != ''   " >
                and message_type = #{messageType,jdbcType=VARCHAR}
            </if>
            <if test="messageContentId !=null and messageContentId != ''   " >
                and message_content_id = #{messageContentId,jdbcType=VARCHAR}
            </if>
            <if test="networkMainBodyId !=null and networkMainBodyId != ''   " >
                and network_main_body_id = #{networkMainBodyId}
            </if>
            <if test="networkMainBodyName !=null and networkMainBodyName != ''   " >
                and network_main_body_name like CONCAT('%',#{networkMainBodyName},'%' )
            </if>
        </trim>
        and message_status in ('0','1')
        order by create_date desc
    </select>

    <select id="checkIsUploadData" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote" resultType="int">
        SELECT
            count(*)
        FROM
            supervise_message_note
        WHERE
        message_content_id = #{messageContentId}
        and message_type = #{messageType}
        and network_main_body_id = #{networkMainBodyId}
        AND message_status = '1'
    </select>

    <select id="checkIsDriverReport" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote" resultType="int">
        SELECT
            count(*)
        FROM
            supervise_message_note m,
            supervise_driver_bus_info b
        WHERE
            m.message_content_id = b.driver_id
          AND b.driving_license = #{drivingLicense}
          AND message_type = 'driver'
          AND network_main_body_id = #{networkMainBodyId}
          AND message_status = '1'

        <if test="delayMinute != null and delayMinute != ''">
              AND m.modify_date <![CDATA[ <= ]]> NOW() - INTERVAL #{delayMinute} MINUTE
        </if>
    </select>

    <!--<select id="checkIsUploadDataByDriver" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote" resultType="int">
        SELECT
            count(0)
        FROM
            supervise_message_note n
        LEFT JOIN supervise_driver_bus_info d ON n.message_content_id = d.driver_id
        WHERE
            n.message_type = 'driver'
        AND d.driving_license = #{messageContentId,jdbcType=VARCHAR}
        AND n.message_status = 1
    </select>-->

    <select id="queryMessageNoteByBusiId" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote" resultType="com.isoftstone.hig.supervise.api.entity.MessageNote">
        select
        message_time, message_status,network_main_body_id,network_main_body_name,result_info
        from supervise_message_note
        where message_content_id = #{messageContentId,jdbcType=VARCHAR}
        <if test="messageType !=null and messageType != ''   " >
            and message_type = #{messageType,jdbcType=VARCHAR}
        </if>
        <if test="networkMainBodyId !=null and networkMainBodyId != ''   " >
            and network_main_body_id = #{networkMainBodyId}
        </if>
        order by create_date desc
    </select>

    <select id="queryReturnMsgByBusiId" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote"
            resultType="com.isoftstone.hig.supervise.api.entity.MessageNote">
        select network_main_body_name,result_info from supervise_message_note
        where message_content_id = #{messageContentId,jdbcType=VARCHAR}
        <if test="messageType !=null and messageType != '' " >
            and message_type = #{messageType,jdbcType=VARCHAR}
        </if>
        <if test="networkMainBodyId !=null and networkMainBodyId != '' " >
            and network_main_body_id = #{networkMainBodyId}
        </if>
        and message_status in ('0','1')
        order by create_date desc
    </select>


    <select id="countSucReportNum" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            supervise_message_note
        WHERE
          message_content_id = #{messageContentId}
          and message_type = #{messageType}
          AND message_status = '1'
    </select>

    <select id="queryReportMessage" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote" resultType="com.isoftstone.hig.supervise.api.entity.MessageNote">
        select
            message_type,message_status,message_content_id,network_main_body_id,report_message,result_message
        from supervise_message_note
        where message_status = '1'
    </select>


    <select id="queryReportingMessage" parameterType="com.isoftstone.hig.supervise.api.entity.MessageNote" resultType="com.isoftstone.hig.supervise.api.entity.MessageNote">
        select
        message_time, message_status,network_main_body_id,network_main_body_name,result_info
        from supervise_message_note
        where
        message_content_id = #{messageContentId,jdbcType=VARCHAR}
        and message_type = #{messageType,jdbcType=VARCHAR}
        and network_main_body_id in
        <foreach collection="networkMainBodyIdList" item="networkMainBodyId" open="(" close=")" separator=",">
            #{networkMainBodyId}
        </foreach>
    </select>
</mapper>
