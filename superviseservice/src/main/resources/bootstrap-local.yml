management:
  health:
    elasticsearch:
      enabled: false
eureka:
  client:
    #是否拉取其他的服务
    fetch-registry: true
    #本地使用时，将此属性设置为false，不会将本地服务注册到eurake上，但是本地服务可以从eurake上获取到服务信息
    register-with-eureka: false
    service-url:
      defaultZone: http://eureka.delta.10000da.vip:9000/eureka/
  instance:
    instance-id: lsds35435435454
    lease-expiration-duration-in-seconds: 20
    lease-renewal-interval-in-seconds: 10
    prefer-ip-address: true

logging:
  config: classpath:log4j2-${spring.profiles.active}.xml

kafka-host: http://kafka.delta.10000da.vip:30700
redis-host: *************
redis-port: 26379
redis-pwd: wlyd2019

rabbitmq-host: rabbitmq-service.delta
rabbitmq-port: 5672
rabbitmq-user: admin
rabbitmq-pwd: admin123
#数据源配置
database-base-url: ************************************************************************

es-host: elasticsearch-service.delta:9200
es-user: elastic
es-pwd: xxM7FsYOkAlDrSNd6gFH
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      filters: stat,log4j2
      initialSize: 5
      maxActive: 20
      maxPoolPreparedStatementPerConnectionSize: 20
      maxWait: 60000
      minEvictableIdleTimeMillis: 300000
      minIdle: 5
      poolPreparedStatements: true
      testOnBorrow: false
      testOnReturn: false
      testWhileIdle: true
      timeBetweenEvictionRunsMillis: 60000
      validationQuery: SELECT 1 FROM DUAL
    type: com.alibaba.druid.pool.DruidDataSource
    url: ${database-base-url}/inner_wlyd_supervise?allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: sit_user
    password: thoh1ayieZ
  flyway:
    baselineOnMigrate: false
    cleanDisabled: false
    enabled: false
    locations: classpath:/db/migration
    outOfOrder: false
    placeholder-replacement: false
  kafka:
    bootstrap-servers: ${kafka-host}
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: true
      group-id: test_supervise
      max-poll-records: 2000
    producer:
      batch-size: 16384
      buffer-memory: 33554432
      linger: 10
      retries: 3
  rabbitmq:
    connection-timeout: 10000
    host: ${rabbitmq-host}
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 1
        max-concurrency: 1
        retry:
          enabled: true
          initial-interval: 5000
          max-attempts: 1
    password: ${rabbitmq-pwd}
    port: 5672
    publisher-confirms: true
    publisher-returns: true
    username: ${rabbitmq-user}
    virtual-host: /supervise
# redis配置
redis:
  host: ${redis-host}
  maxActive: 200
  maxIdle: 20
  maxWait: 3000
  minIdle: 5
  password: ${redis-pwd}
  port: ${redis-port}
  testOnBorrow: true
  testOnReturn: true
  timeout: 3000
feign:
  httpclient:
    connection-timeout: 7000
ribbon:
  ConnectTimeout: 7000
  ReadTimeout: 7000
jwt:
  auth:
    account: /info
    path: /login
  expiration: 10080
  header: Authorization
  secret: aaHR0cHM6Ly9teS5vc2NoaW5hLm5ldC91LzM2ODE4Njg
xxl:
  token: xxl-job-delta
  host: http://xxl.delta.10000da.vip:8080/xxl-job-admin
  job:
    accessToken: ${xxl.token}
    admin:
      addresses: ${xxl.host}
    executor:
      address: ''
      appname: xxl-job-supervise
      ip: ''
      logpath: /tmp/applogs/xxl-job/supervise/jobhandler
      logretentiondays: 10
      port: 0
#针对实际承运人处理不同的网络货运主体
carrier:
  configs:
    networkMainBodyIdList:
      - 5fc67fecf01b4ae3a87734c08f382962 #万联易达物流科技有限公司舞阳分公司
      - eaa2182d685a43d0b9514fbcd0f8ffe1 #太原万联易达供应链管理有限公司南陵分公司
      - e4d7680f7f434824be83b982133a4d50 #芜湖万联易达智慧物流有限公司
    checkChannelList:
      - neimengNew
wlyd:
  sync:
    networkMainBodyIdList:
      - ed7a30841ab14d03b46e59124eda5765

