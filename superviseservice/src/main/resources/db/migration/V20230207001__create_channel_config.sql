
-- ------------supervise_channel_config--------------
CREATE TABLE `supervise_channel_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `cust_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网络货运主体id',
  `cust_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网络货运主体名称',
  `channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '渠道名称',
  `channel_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '渠道url',
  `check_default` int DEFAULT NULL COMMENT '是否默认选择 0否 1是',
  `ex_cust_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平行账号网络货运主体id，多个逗号分隔（解决平行帐号问题，真假货运主体id都得填写）',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监管渠道配置';


INSERT INTO `supervise_channel_config` VALUES (1, '2', '万联易达物流科技有限公司', 'tianjin', 'https://manage.10000da.cn/gateway/tianjin/report/', 0, '2');
INSERT INTO `supervise_channel_config` VALUES (2, 'd46c346ef5ed43f8b680c2ebdba38f0b', '万联易达物流科技有限公司丰镇市分公司', 'neimeng', 'https://manage.10000da.cn/gateway/neimeng/report/', 1, 'd46c346ef5ed43f8b680c2ebdba38f0b');
