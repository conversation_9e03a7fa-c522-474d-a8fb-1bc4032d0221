#注册中心
register-center-ip-port: register.10000da.vip:21000
eureka:
  client:
    service-url:
      defaultZone: http://${register-center-ip-port}/eureka/
  instance:
    lease-expiration-duration-in-seconds: 20
    lease-renewal-interval-in-seconds: 20
    prefer-ip-address: true
# spring相关
spring:
  cloud:
    nacos:
      username: nacos # 用户名
      password: wlyd2018 # 密码
      config:
        enabled: true      #是否启用
        file-extension: yaml
        namespace: settlement
        server-addr: http://*************:8848
        shared-configs[0]: # 多服务间共享的配置列表
          data-id: common.yaml # 要共享的配置文件id
          refresh: true   # 是否动态刷新，默认为false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      filters: stat,log4j2
      initialSize: 5
      maxActive: 20
      maxPoolPreparedStatementPerConnectionSize: 20
      maxWait: 60000
      minEvictableIdleTimeMillis: 300000
      minIdle: 5
      poolPreparedStatements: true
      testOnBorrow: false
      testOnReturn: false
      testWhileIdle: true
      timeBetweenEvictionRunsMillis: 60000
      validationQuery: SELECT 1 FROM DUAL
    type: com.alibaba.druid.pool.DruidDataSource
    url: ***********************************************************************************************************************************************************************************************************************************
    username: sit_user
    password: thoh1ayieZ
  flyway:
    baselineOnMigrate: false
    cleanDisabled: false
    enabled: false
    locations: classpath:/db/migration
    outOfOrder: false
    placeholder-replacement: false
  kafka:
    bootstrap-servers: kafka.10000da.vip:9092
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: true
      group-id: test_supervise
      max-poll-records: 2000
    producer:
      batch-size: 16384
      buffer-memory: 33554432
      linger: 10
      retries: 3
  rabbitmq:
    connection-timeout: 10000
    host: rabbitmq.10000da.vip
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 1
        max-concurrency: 1
        retry:
          enabled: true
          initial-interval: 5000
          max-attempts: 1
    password: guest
    port: 5672
    publisher-confirms: true
    publisher-returns: true
    username: guest
    virtual-host: /supervise
# redis配置
redis:
  host: redis.10000da.vip
  maxActive: 200
  maxIdle: 20
  maxWait: 3000
  minIdle: 5
  password: 123456
  port: 6379
  testOnBorrow: true
  testOnReturn: true
  timeout: 3000
feign:
  httpclient:
    connection-timeout: 7000
ribbon:
  ConnectTimeout: 7000
  ReadTimeout: 7000
jwt:
  auth:
    account: /info
    path: /login
  expiration: 10080
  header: Authorization
  secret: aaHR0cHM6Ly9teS5vc2NoaW5hLm5ldC91LzM2ODE4Njg
#rbmq:
#  configs:
#    - channel: tianjin
#      channelUrl: http://3pldelta.10000da.vip/gateway/tianjin/report/
#      checkDefault: 0
#      custId: 2
#      custName: 万联易达物流科技有限公司
#    - channel: neimeng
#      channelUrl: http://3pldelta.10000da.vip/gateway/neimeng/report/
#      checkDefault: 0
#      custId: d46c346ef5ed43f8b680c2ebdba38f0b
#      custName: 万联易达物流科技有限公司丰镇市分公司
#    - channel: neimeng
#      channelUrl: http://3pldelta.10000da.vip/gateway/neimeng/report/
#      checkDefault: 0
#      custId: ef8c630729b34e5abe0f1d9a30573021
#      custName: 太原万联易达供应链管理有限公司霍林郭勒分公司
xxl:
  job:
    accessToken: default_token
    admin:
      addresses: http://xxljob.10000da.vip:8090/xxl-job-admin/
    executor:
      address: ''
      appname: xxl-job-supervise
      ip: ''
      logpath: /tmp/applogs/xxl-job/supervise/jobhandler
      logretentiondays: 30
      port: 3506
#针对实际承运人处理不同的网络货运主体
carrier:
  configs:
    networkMainBodyIdList:
      - 5fc67fecf01b4ae3a87734c08f382962 #万联易达物流科技有限公司舞阳分公司
      - eaa2182d685a43d0b9514fbcd0f8ffe1 #太原万联易达供应链管理有限公司南陵分公司
      - e4d7680f7f434824be83b982133a4d50 #芜湖万联易达智慧物流有限公司
    checkChannelList:
      - neimengNew
