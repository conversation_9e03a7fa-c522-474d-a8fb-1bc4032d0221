
package com.isoftstone.hig.supervise.service.domain.model.entity;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 上报合同信息领域实体
 */
@Data
public class SpvContractRptEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同类型[1托运合同 2承运合同]
     */
    private Integer contractType;

    /**
     * 甲方
     */
    private String partyA;

    /**
     * 乙方
     */
    private String partyB;

    /**
     * 合同金额
     */
    private BigDecimal contractAmount;

    /**
     * 合同签订时间
     */
    private Date contractSignTime;

    /**
     * 合同照片访问地址
     */
    private String contractPhotoAccessUrl;

    /**
     * 有效期起
     */
    private Date validStartDate;

    /**
     * 有效期止
     */
    private Date validEndDate;

    /**
     * 网络货运主体id
     */
    private String networkMainBodyId;

    /**
     * 网络货运主体名称
     */
    private String networkMainBodyName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态[4上报成功 5上报失败]
     */
    private String status;

    /**
     * 上报时间
     */
    private Date sendToProDateTime;

    /**
     * 返回结果
     */
    private String resultInfo;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新人id
     */
    private String lastUpdaterId;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
}
