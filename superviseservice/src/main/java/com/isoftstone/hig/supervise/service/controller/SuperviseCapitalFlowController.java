package com.isoftstone.hig.supervise.service.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONUtil;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.CustomEvent;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.framework.component.file.utils.ExcelUtil;
import com.isoftstone.hig.supervise.api.entity.SyncCapitalFlowVo;
import com.isoftstone.hig.supervise.api.filter.*;
import com.isoftstone.hig.supervise.api.inter.SuperviseCapitalFlowInter;
import com.isoftstone.hig.supervise.api.service.EnumMappingService;
import com.isoftstone.hig.supervise.api.service.SuperviseCapitalFlowService;
import com.isoftstone.hig.tms.api.mvcvo.invertshort.dto.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 资金流水
 */
@Slf4j
@RestController
public class SuperviseCapitalFlowController implements SuperviseCapitalFlowInter{

    @Autowired
    private SuperviseCapitalFlowService superviseCapitalFlowService;

    @Autowired
    private EnumMappingService enumMappingService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 列表查询
     * @param pagingInfo
     * @return
     */
    @LogPrintPoint
    @Override
    public ResultMode<SuperviseCapitalFlowListRespVo> getSuperviseCapitalFlowList(PagingInfo<SuperviseCapitalFlowListReqVo> pagingInfo) {
        try {
            return superviseCapitalFlowService.getSuperviseCapitalFlowList(pagingInfo);
        }catch (Exception e){
            log.error("资金流水列表查询异常:",e);
            return ResultMode.fail("资金流水列表查询异常！");
        }
    }

    /**
     * 列表查询导出
     * @param superviseCapitalFlowListReqVo
     * @return
     */
    @Override
    public void exportSuperviseCapitalFlowList(HttpServletRequest request,
                                                     HttpServletResponse response,
                                                     SuperviseCapitalFlowListReqVo superviseCapitalFlowListReqVo) {
        try {
            PagingInfo<SuperviseCapitalFlowListReqVo> pageInfo = new PagingInfo<>();
            pageInfo.setFilterModel(superviseCapitalFlowListReqVo);
            pageInfo.setPageLength(200);
            pageInfo.setCurrentPage(1);
            ResultMode<SuperviseCapitalFlowListRespVo> currentPageData = null;
            List<SuperviseCapitalFlowListRespVo> exportData = new ArrayList<>();
            do{
                currentPageData = superviseCapitalFlowService.getSuperviseCapitalFlowList(pageInfo);
                if(CollUtil.isNotEmpty(currentPageData.getModel())){
                    List<SuperviseCapitalFlowListRespVo> pageData = currentPageData.getModel();
                    exportData.addAll(pageData);
                    pageInfo.setCurrentPage(pageInfo.currentPage + 1);
                }
            }while (CollUtil.isNotEmpty(currentPageData.getModel()));

            if(CollUtil.isEmpty(exportData)){
                return;
            }
            //上报状态:20200727102223100001：上报方式:20231215170000100001
            List<String> dictIdList = Arrays.asList("20200727102223100001","20231215170000100001");
            Map<String, Map<String,String>> dictMap = enumMappingService.getDictByIds(dictIdList);
            for (SuperviseCapitalFlowListRespVo exportDatum : exportData) {
                exportDatum.setStatusStr(dictMap.get("20200727102223100001").get(exportDatum.getStatus()));

                //上报方式
                if (!StrUtils.isBlank(exportDatum.getAutoReportStatus()) && dictMap.containsKey("20231215170000100001")){
                    exportDatum.setAutoReportStatusStr(dictMap.get("20231215170000100001").get(exportDatum.getAutoReportStatus()));
                }
            }
            ClassPathResource resource = new ClassPathResource("verifyxml/ExportCapitalFlow.xml");
            try(InputStream is = resource.getInputStream();){
                ExcelUtil.exportDataToExcel(request, response, "资金流水", is, exportData, "资金流水",
                    JwtUtil.getInstance().getUsernameByToken() + "(" + JwtUtil.getInstance().getLoginNameByToken() + ")");
            }catch (Exception e){
                log.error("资金流水导出异常：",e);
            }
        }catch (Exception e){
            log.error("资金流水列表导出异常:",e);
        }
    }


    /**
     * 删除
     * @param superviseCapitalFlowDelReqVo
     * @return
     */
    @LogPrintPoint
    @Override
    public ResultMode delSuperviseCapitalFlowInfo(SuperviseCapitalFlowDelReqVo superviseCapitalFlowDelReqVo) {
        try {
            return superviseCapitalFlowService.delSuperviseCapitalFlowInfo(superviseCapitalFlowDelReqVo);
        } catch (Exception e) {
            log.error("删除资金流水异常:", e);
            return ResultMode.fail("删除资金流水异常！");
        }
    }

    /**
     * 批量删除
     */
    @LogPrintPoint
    @Override
    public ResultMode delSuperviseCapitalFlowBatch(List<String> docNumList) {
        try {
            return superviseCapitalFlowService.delSuperviseCapitalFlowBatch(docNumList);
        } catch (Exception e) {
            log.error("批量删除资金流水异常:", e);
            return ResultMode.fail("批量删除资金流水异常！");
        }
    }

    /**
     * 更新
     * @param superviseCapitalFlowReportDetailReqVo
     * @return
     */
    @LogPrintPoint
    @Override
    public ResultMode updateSuperviseCapitalFlowReportDetail(UpdateSuperviseCapitalFlowReportDetailReqVo superviseCapitalFlowReportDetailReqVo) {
        try {
            superviseCapitalFlowService.updateSuperviseCapitalFlowReportDetail(superviseCapitalFlowReportDetailReqVo);
            return ResultMode.success();
        }catch (Exception e){
            log.error("更新资金流水异常:",e);
            return ResultMode.fail("更新资金流水异常！");
        }
    }

    /**
     * 上报
     */
    @LogPrintPoint
    @Override
    public ResultMode submitCapitalFlow(UpdateSuperviseCapitalFlowReportDetailReqVo capitalFlowVo) {
        try {
            return superviseCapitalFlowService.submitCapitalFlow(capitalFlowVo);
        }catch (Exception e){
            log.error("上报资金流水异常:",e);
            return ResultMode.fail("上报资金流水异常！");
        }
    }

    /**
     * 批量上报
     */
    @LogPrintPoint
    @Override
    public ResultMode batchReportCapitalFlow(List<String> docNumList) {
        try {
            return superviseCapitalFlowService.batchReportCapitalFlow(docNumList);
        }catch (Exception e){
            log.error("批量上报资金流水异常:",e);
            return ResultMode.fail("批量上报资金流水异常");
        }
    }

    /**
     * 重新上报
     */
    @LogPrintPoint
    @Override
    public ResultMode reportAgain(UpdateSuperviseCapitalFlowReportDetailReqVo capitalFlowVo) {
        try {
            return superviseCapitalFlowService.reportAgain(capitalFlowVo);
        }catch (Exception e){
            log.error("重新上报异常:",e);
            return ResultMode.fail("重新上报异常！");
        }
    }

    /**
     * 查看详情
     */
    @Override
    public ResultMode<SuperviseCapitalFlowReportDetailRespVo> viewDetail(SuperviseCapitalFlowReportDetailReqVo reqVo) {
        try {
            SuperviseCapitalFlowReportDetailRespVo respVo = superviseCapitalFlowService.getSuperviseCapitalFlowReportDetail(reqVo);
            if (respVo == null) {
                return ResultMode.success();
            }
            return ResultMode.success(respVo);
        }catch (Exception e){
            log.error("查看详情异常：",e);
            return ResultMode.fail("查看详情异常！");
        }
    }

    /**
     * 查看映射结果
     */
    @LogPrintPoint
    @Override
    public ResultMode<SuperviseCapitalFlowListRespVo> viewCapitalFlowReportDetail(SuperviseCapitalFlowReportDetailReqVo superviseCapitalFlowReportDetailReqVo) {
        try {
            SuperviseCapitalFlowReportDetailRespVo respVo = superviseCapitalFlowService.viewCapitalFlowReportDetail(superviseCapitalFlowReportDetailReqVo);
            if (respVo == null) {
                return ResultMode.success();
            }
            return ResultMode.success(respVo);
        }catch (Exception e){
            log.error("查看映射结果异常：",e);
            return ResultMode.fail("查看映射结果异常！");
        }
    }

    /**
     * 消息处理
     * @param syncCapitalFlowVo
     * @return
     */
    @PostMapping("/saveCapitalFlow")
    public ResultMode saveCapitalFlow(@RequestBody SyncCapitalFlowVo syncCapitalFlowVo){
        CustomEvent event = new CustomEvent("","sync_tob_capital_flow_to_supervise",JSONUtil.toJsonStr(syncCapitalFlowVo));
        applicationEventPublisher.publishEvent(event);
        return ResultMode.success();
    }

}
