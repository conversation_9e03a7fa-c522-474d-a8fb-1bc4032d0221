package com.isoftstone.hig.supervise.service;

import com.isoftstone.hig.bms.api.client.BmsSuperviseClient;
import com.isoftstone.hig.common.utils.*;
import com.isoftstone.hig.common.utils.exception.WlydCommExceptionHandler;
import com.isoftstone.hig.platform.api.inter.*;
import com.isoftstone.hig.supervise.service.enmus.KaFkaTopicNameEnmu;
import com.isoftstone.hig.supervise.service.kafka.KafkaCustomMsgListener;
import com.isoftstone.hig.tms.api.inter.TmsWaybillInter;
import com.wanlianyida.dcs.service.DcsWaybillService;
import com.wanlianyida.dcs.service.MessageService;
import com.wanlianyida.dcs.service.OrderReportingService;
import com.wanlianyida.srs.api.inter.SRSReportInter;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 消息服务
 *
 * <AUTHOR>
 */
@EnableEurekaClient
@Import({WlydCommExceptionHandler.class})
@SpringBootApplication
@EnableTransactionManagement
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableAsync
@ComponentScan(basePackages = "com.isoftstone.hig")
@MapperScan({"com.isoftstone.hig.supervise.service.infrastructure.repository.mapper"})
@FeignClient
@EnableFeignClients(clients = {
        PlatformCmDictionaryInter.class,
        PlatformCmOperationMainBodyInter.class,
        IPlatformKafkaHandleInter.class,
        TmsWaybillInter.class,
        BmsSuperviseClient.class,
        MessageService.class,
        PlatformCmCityInter.class,
        PlatformCmPlatformParameterInter.class,
        DcsWaybillService.class,
        OrderReportingService.class,
        SRSReportInter.class
})
public class SuperviseServiceApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext ctx = SpringApplication.run(SuperviseServiceApplication.class, args);

        //初始化Jwt帮助类
        JwtUtil.getInstance().init(ctx);

        //初始化上下文
        SpringContextUtil.setApplicationContext(ctx);

        //SpringContextUtil.setKafkaTopicPrefix("settlement_");

        //初始化Redis
        RedisUtil.initialPool();

        //kafka处理
        KafkaConfig.getInstance().initProducer(ctx);
        KafkaConfig.getInstance().initConsumer(ctx);
        KafkaUtil kaf = SpringContextUtil.getBeanByClass(KafkaUtil.class);
        kaf.kafkaListenerConsumer(new String[]{
                        SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.SUPERVISE_DRIVER.getTopicName(),
                        SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.SUPERVISE_VEHICLE.getTopicName(),
                        SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.SUPERVISE_CAPITAL_ACCOUNT.getTopicName(),
                        "uat_"+ UtilityEnum.KafkaTopicNameEnum.SUPERVISE_CAPITAL_ACCOUNT.getTopicName(),
                SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.SUPERVISE_WAYBILL.getTopicName(),
                SpringContextUtil.getKafkaTopicPrefix() + KaFkaTopicNameEnmu.BMS_UPDATE_SUPERVISE_WAYBILL_BUS_INFO_STATUS.getTopicName(),
                SpringContextUtil.getKafkaTopicPrefix() + KaFkaTopicNameEnmu.TERMINATION_PAY.getTopicName(),
                SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.TMS_SYN_WAYBILLTO_SUPERVISE_PAYEE.getTopicName(),
                KaFkaTopicNameEnmu.SYNC_TOB_CAPITAL_FLOW_TO_SUPERVISE.getTopicName()
            }
        );
        ctx.addApplicationListener(new KafkaCustomMsgListener());

    }
}
