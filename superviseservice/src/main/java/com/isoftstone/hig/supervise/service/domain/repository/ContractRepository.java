package com.isoftstone.hig.supervise.service.domain.repository;

import com.isoftstone.hig.supervise.service.domain.model.condition.ContractCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvContractBusInfoEntity;

import java.util.List;

/**
 * 合同信息操作
 */
public interface ContractRepository {

    List<SpvContractBusInfoEntity> queryList(ContractCondition condition);

    /**
     * 删除
     */
    void delete(Long id);

    SpvContractBusInfoEntity getDetail(Long id);
}
