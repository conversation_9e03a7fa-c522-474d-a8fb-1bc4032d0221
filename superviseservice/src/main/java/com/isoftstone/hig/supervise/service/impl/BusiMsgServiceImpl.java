package com.isoftstone.hig.supervise.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.DateUtils;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.supervise.api.entity.*;
import com.isoftstone.hig.supervise.api.filter.SuperviseCapitalFlowReportDetailRespVo;
import com.isoftstone.hig.supervise.api.service.BusiMsgService;
import com.isoftstone.hig.supervise.api.service.ChannelConfigService;
import com.isoftstone.hig.supervise.api.service.MessageNoteService;
import com.isoftstone.hig.supervise.service.application.model.command.ActualCarrierInfoReportCommand;
import com.isoftstone.hig.supervise.service.application.model.command.ContractInfoReportCommand;
import com.isoftstone.hig.supervise.service.application.model.command.ShipperInfoReportCommand;
import com.isoftstone.hig.supervise.service.application.model.dto.CarrierViewDTO;
import com.isoftstone.hig.supervise.service.application.model.dto.ContractViewDTO;
import com.isoftstone.hig.supervise.service.application.model.dto.ShipperViewDTO;
import com.isoftstone.hig.supervise.service.exchange.SrsExchangeService;
import com.isoftstone.hig.supervise.service.rabbitmq.RabbitConstants;
import com.isoftstone.hig.supervise.service.repository.EnumMappingRepository;
import com.isoftstone.hig.supervise.service.utils.StringUtils;
import com.wanlianyida.dcs.entity.Message;
import com.wanlianyida.dcs.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import javax.annotation.Resource;

@Slf4j
@Service
public class BusiMsgServiceImpl implements BusiMsgService{

    @Autowired
    private ChannelConfigService channelConfigService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private MessageService messageService;

    @Autowired
    private MessageNoteService messageNoteService;

    @Resource
    EnumMappingRepository enumMappingRepository;

    @Autowired
    private SrsExchangeService srsExchangeService;

    @Override
    public ResultMode convertAndSend(BaseRabbitMessageVO message) {
        String routingKey = "";
        Set<String> networkIdSet = new HashSet<>();
        if(!StringUtils.isEmpty(message.getNetworkMainBodyIdList())){
            networkIdSet.addAll(message.getNetworkMainBodyIdList());
        }
        if(!StringUtils.isEmpty(message.getNetworkMainBodyId())){
            networkIdSet.add(message.getNetworkMainBodyId());
        }

        List<SuperviseChannelConfig> configList = new ArrayList<>();
        if(StringUtils.isEmpty(networkIdSet)){
            //司机和车辆发送默认配置的网络货运主体
            configList = channelConfigService.getDefaultCheckChannel();
            //过滤挂车上报渠道
//            if (StrUtil.equals(message.getBusiType(), RabbitConstants.BusiType.WLHY_VEHICLE)
//                && StrUtil.endWith(message.getBusiId(), "挂")) {
//                configList = channelConfigService.getTrailerReportChannel(configList);
//            }
           /* routingKey = getRoutingKey(message,null);
            rabbitTemplate.convertAndSend(RabbitConstants.TOPIC_EXCHANGE, routingKey, JSON.toJSONString(message),new CorrelationData(UtilityClass.uuid()));*/
        }else{
            for(String networkId:networkIdSet){
                SuperviseChannelConfig config = channelConfigService.getConfigByCustId(networkId);
                if(config==null){
                    log.info("渠道没有找到:"+networkId);
                    continue;
                }
                configList.add(config);
            }
        }

        for(SuperviseChannelConfig config:configList){
            message.setNetworkMainBodyId(config.getCustId());
            message.setNetworkMainBodyName(config.getCustName());
            routingKey = getRoutingKey(message,config.getCustId());
            if(StringUtils.isEmpty(routingKey)){
                log.info("routingKey没有找到:"+config.getCustId());
                continue;
            }
            rabbitTemplate.convertAndSend(RabbitConstants.TOPIC_EXCHANGE, routingKey, JSON.toJSONString(message),new CorrelationData(UtilityClass.uuid()));
        }

        return ResultMode.success();
    }


    private void handReportNetworkMainBody(BaseRabbitMessageVO message,String networkId){
        SuperviseChannelConfig config = channelConfigService.getConfigByCustId(networkId);
        if(config==null){
            return;
        }
        Date curDate = new Date();
        String userBaseId = StrUtil.blankToDefault(JwtUtil.getInstance().getUserBaseIdByToken(), "");
        log.info("handReportNetworkMainBody#messageType:{}, messageContentId:{}, userBaseId:{}", message.getBusiType(), message.getBusiId(), userBaseId);
        MessageNote messageNote = new MessageNote();
        messageNote.setMessageType(message.getBusiType());
        messageNote.setMessageContentId(message.getBusiId());
        messageNote.setMessageStatus("2");
        messageNote.setCreateDate(curDate);
        messageNote.setModifyDate(curDate);
        messageNote.setCreateBy(userBaseId);
        messageNote.setModifyBy(userBaseId);
        messageNote.setNetworkMainBodyId(config.getCustId());
        messageNote.setNetworkMainBodyName(config.getCustName());
        messageNote.setResultInfo(null);
        messageNote.setReportMessage(null);
        messageNote.setResultMessage(null);
        messageNote.setMessageTime(null);
        messageNoteService.insertReportMsg(messageNote);
    }

    private String getRoutingKey(BaseRabbitMessageVO message,String networkMainBodyId){
        String routingKey = "";
        String channel = channelConfigService.getChannel(networkMainBodyId);
        switch (message.getBusiType()) {
            case RabbitConstants.BusiType.WLHY_DRIVER:
                if(StringUtils.isEmpty(networkMainBodyId)){
                    routingKey = RabbitConstants.RoutingKeyEnum.DRIVIER_KEY.getRoutingKey(channel);
                }else{
                    routingKey = RabbitConstants.RoutingKeyEnum.DRIVIER_CHANNEL_KEY.getRoutingKey(channel);
                }
                break;
            case RabbitConstants.BusiType.WLHY_VEHICLE:
                if(StringUtils.isEmpty(networkMainBodyId)){
                    routingKey = RabbitConstants.RoutingKeyEnum.VEHICLE_KEY.getRoutingKey(channel);
                }else{
                    routingKey = RabbitConstants.RoutingKeyEnum.VEHICLE_CHANNEL_KEY.getRoutingKey(channel);
                }
                break;
            case RabbitConstants.BusiType.WLHY_ACCOUNT:
                routingKey = RabbitConstants.RoutingKeyEnum.ACCOUNT_KEY.getRoutingKey(channel);
                break;
            case RabbitConstants.BusiType.WLHY_WAYBILL:
                routingKey = RabbitConstants.RoutingKeyEnum.WAYBILL_KEY.getRoutingKey(channel);
                break;
            case RabbitConstants.BusiType.WLHY_SHIPPER:
                routingKey = RabbitConstants.RoutingKeyEnum.SHIPPER_KEY.getRoutingKey(channel);
                break;
            case RabbitConstants.BusiType.WLHY_ACTUAL_CARRIER:
                routingKey = RabbitConstants.RoutingKeyEnum.ACTUAL_CARRIER_KEY.getRoutingKey(channel);
                break;
            case RabbitConstants.BusiType.WLHY_CONTRACT:
                routingKey = RabbitConstants.RoutingKeyEnum.CONTRACT_KEY.getRoutingKey(channel);
                break;
            default:
                return null;
        }
        return routingKey;
    }

    @Override
    public ResultMode setNetworkId(BaseRabbitMessageVO message) {
        Set<String> networkIdSet = new HashSet<>();
        if(!StringUtils.isEmpty(message.getNetworkMainBodyIdList())){
            networkIdSet.addAll(message.getNetworkMainBodyIdList());
        }
        if(!StringUtils.isEmpty(message.getNetworkMainBodyId())){
            networkIdSet.add(message.getNetworkMainBodyId());
        }
        //设置上报id
        message.setReportNum(channelConfigService.getChannelNum());

        ResultMode resultMode = checkChannel(message, networkIdSet);
        if(!resultMode.isSucceed()){
            return resultMode;
        }

        if(StringUtils.isEmpty(message.getNetworkMainBodyIdList())){
//            //过滤挂车上报渠道
//            if (StrUtil.equals(message.getBusiType(), RabbitConstants.BusiType.WLHY_VEHICLE)
//                && StrUtil.endWith(message.getBusiId(), "挂")) {
//                networkIdSet.addAll(channelConfigService.getDefaultTrailerCustIds());
//            } else {
                networkIdSet.addAll(channelConfigService.getDefaultCustIds());
//            }
            for(String networkId:networkIdSet){
                //网络货运主体上报校验
//                resultMode = checkNetworkMainBodyReport(message, networkId);
//                if(!resultMode.isSucceed()){
//                    return resultMode;
//                }
                handReportNetworkMainBody(message,networkId);
            }
        }else{
            for(String networkId:networkIdSet){
                //网络货运主体上报校验
//                resultMode = checkNetworkMainBodyReport(message, networkId);
//                if(!resultMode.isSucceed()){
//                    return resultMode;
//                }
                //处理上报的网络货运主体
                handReportNetworkMainBody(message,networkId);
            }
        }
        message.setNetworkMainBodyIdList(new ArrayList<>(networkIdSet));
        return ResultMode.success();
    }

    /**
     * 网络货运主体上报校验
     *
     * @param message
     * @param networkId
     * @return com.isoftstone.hig.common.model.ResultMode
     * <AUTHOR>
     * @Date 2023/7/20 18:22
     */
    /*private ResultMode checkNetworkMainBodyReport(BaseRabbitMessageVO message, String networkId) {
        //内蒙监管的牵引车，必须上报挂车 校验
        SuperviseChannelConfig config = channelConfigService.getConfigByCustId(networkId);
        if (ObjectUtil.isNotEmpty(config) && config.getReportTrailer() == 1
            && StrUtil.equals(message.getBusiType(), RabbitConstants.BusiType.WLHY_VEHICLE)){
            Vehicle vehicle = (Vehicle) message;
            if (StrUtil.startWith(vehicle.getVehicleType(), "Q") && StrUtil.isBlank(vehicle.getTrailerVehiclePlateNumber())) {
                return ResultMode.fail("上报车辆["+vehicle.getVehicleNumber()+"]为\"牵引车\"，挂车车牌号为必填");
            }
        }
        return ResultMode.success();
    }*/

    private ResultMode checkChannel(BaseRabbitMessageVO message, Set<String> networkIdSet){
        ResultMode resultMode = null;
        if(StringUtils.isEmpty(networkIdSet)){
            resultMode = checkChannel(message, "");
            return resultMode;
        }else{
            for(String networkId:networkIdSet){
                resultMode =  checkChannel(message, networkId);
                if(!resultMode.isSucceed()){
                    return resultMode;
                }
            }
        }
        return ResultMode.success();
    }

    private ResultMode checkChannel(BaseRabbitMessageVO message, String networkMainBodyId){
        String channel = channelConfigService.getChannel(networkMainBodyId);
        switch (message.getBusiType()) {
            case RabbitConstants.BusiType.WLHY_DRIVER:
            case RabbitConstants.BusiType.WLHY_VEHICLE:
            case RabbitConstants.BusiType.WLHY_SHIPPER:
            case RabbitConstants.BusiType.WLHY_ACTUAL_CARRIER:
            case RabbitConstants.BusiType.WLHY_CONTRACT:
                if(!StringUtils.isEmpty(networkMainBodyId)){
                    if(StringUtils.isEmpty(channel)){
                        return ResultMode.fail("渠道没有配置:"+networkMainBodyId);
                    }
                }else{
                    //司机和车辆没有传网络货运主体时，判断是否配置默认渠道
                    List<SuperviseChannelConfig> configList = channelConfigService.getDefaultCheckChannel();
                    //过滤挂车上报渠道
//                    if (StrUtil.equals(message.getBusiType(), RabbitConstants.BusiType.WLHY_VEHICLE)
//                        && StrUtil.endWith(message.getBusiId(), "挂")) {
//                        configList = channelConfigService.getTrailerReportChannel(configList);
//                    }
                    if(StringUtils.isEmpty(configList)){
                        return ResultMode.fail("没有配置默认渠道，不能上报");
                    }
                }
                break;
            case RabbitConstants.BusiType.WLHY_ACCOUNT:
                if(StringUtils.isEmpty(networkMainBodyId)){
                    return ResultMode.fail("资金信息的网络主体不能为空");
                }
                if(StringUtils.isEmpty(channel)){
                    return ResultMode.fail("渠道没有配置:"+networkMainBodyId);
                }
                break;
            case RabbitConstants.BusiType.WLHY_WAYBILL:
                if(StringUtils.isEmpty(networkMainBodyId)){
                    return ResultMode.fail("运单信息的网络主体不能为空");
                }
                if(StringUtils.isEmpty(channel)){
                    return ResultMode.fail("渠道没有配置:"+networkMainBodyId);
                }
                break;
            default:
                return ResultMode.fail("没有找到业务类型："+message.getBusiType());
        }
        return ResultMode.success();
    }


    @Override
    public ResultMode queryMappingResults(BaseRabbitMessageVO message) {
        if(StringUtils.isEmpty(message.getNetworkMainBodyId())){
            return ResultMode.fail("网络货运主体为空");
        }
        try {
            String channel = channelConfigService.getChannel(message.getNetworkMainBodyId());
            String result = null;
            if(StrUtil.equals(channel,RabbitConstants.SRS)) {
                result = this.feignInvokeMappingTarget(message);
            } else {
                result = this.httpInvokeMappingTarget(message);
            }
            if(StringUtils.isEmpty(result)){
                return ResultMode.fail("返回据数为空");
            }
            result = toResultMode(result);
            if(StringUtils.isEmpty(result)){
                return ResultMode.fail("返回据数为空");
            }

            Object object = parseData(message.getBusiType(),result, channel);
            return ResultMode.success("suc",object);
        } catch (Exception e) {
            log.error("查询映射结果异常:{}",e);
            return ResultMode.fail("没有找到业务类型");
        }
    }

    private Object parseData(String busiType, String json, String channel){
        try {
            if(StringUtils.isEmpty(json)){
                return null;
            }
            switch (busiType) {
                case RabbitConstants.BusiType.WLHY_DRIVER:
                    return JSON.parseObject(json, Driver.class);
                case RabbitConstants.BusiType.WLHY_VEHICLE:
                    return JSON.parseObject(json, Vehicle.class);
                case RabbitConstants.BusiType.WLHY_SHIPPER:
                    return JSON.parseObject(json, ShipperViewDTO.class);
                case RabbitConstants.BusiType.WLHY_ACTUAL_CARRIER:
                    return JSON.parseObject(json, CarrierViewDTO.class);
                case RabbitConstants.BusiType.WLHY_CONTRACT:
                    return JSON.parseObject(json, ContractViewDTO.class);
                case RabbitConstants.BusiType.WLHY_WAYBILL:
                    return JSON.parseObject(json, Waybill.class);
                case RabbitConstants.BusiType.WLHY_ACCOUNT:
                    if (StrUtil.equals(channel, RabbitConstants.SRS)) {
                        return JSON.parseObject(json, SuperviseCapitalFlowReportDetailRespVo.class);
                    }
                    return JSON.parseObject(json, CapitalAccount.class);
                case RabbitConstants.BusiType.WLHY_CAPITAL_FLOW:
                    return JSON.parseObject(json, SuperviseCapitalFlowReportDetailRespVo.class);
                default:
                   break;
            }
        } catch (Exception e) {
            log.error("转JSON异常:{}",e);
        }
        return null;
    }

    private String toResultMode(String result){
        try {
            ResultMode resJson = JSON.parseObject(result,ResultMode.class);
            if(resJson.getModel()!=null && resJson.getModel().size()>0){
                JSONObject jsonObject = (JSONObject)resJson.getModel().get(0);
                return jsonObject.toJSONString();
            }
        } catch (Exception e) {
            log.error("result={}",result);
            log.error("转换ResultMode异常：{}",e);
        }
        return null;
    }

    @Override
    public void sendDcsMessage(String module, String bizId, String requestMsg, String responseMsg, String status) {
        try {
            com.wanlianyida.dcs.entity.Message message = new com.wanlianyida.dcs.entity.Message();
            message.setBizId(bizId);
            message.setService(RabbitConstants.DcsData.DCS_SERVICE_CODE);
            message.setModule(module);
            message.setRequest(requestMsg);
            message.setResponse(responseMsg);
            message.setStatus(status);
            message.setCreatDate(DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss.SSS"));
            String dcsResult = messageService.send(message);
            if(!dcsResult.equals(RabbitConstants.DcsData.DCS_SUCCESS)){
                log.info("发送dcs错误：模块:{},业务id：{}",module,bizId);
            }
        } catch (Exception e) {
            log.error("发送dcs异常：模块:{},业务id：{},异常:{}",module,bizId,e);
        }
    }

    @Override
    public List<Message> queryDcsMessage(String module, String bizId) {
        com.wanlianyida.dcs.vo.MessageVo message = new com.wanlianyida.dcs.vo.MessageVo();
        message.setBizId(bizId);
        message.setService(RabbitConstants.DcsData.DCS_SERVICE_CODE);
        message.setModule(module);
        return messageService.query(message);
    }


    /**
     * http调用映射信息
     * @param message
     * @return
     */
    private String httpInvokeMappingTarget(BaseRabbitMessageVO message) {
        String channelUrl = channelConfigService.getChannelUrl(message.getNetworkMainBodyId());
        if(StringUtils.isEmpty(channelUrl)){
            log.info("httpInvokeMappingTarget#没有配置渠道:{}",message.getNetworkMainBodyId());
            return null;
        }

        switch (message.getBusiType()) {
            case RabbitConstants.BusiType.WLHY_DRIVER:
                channelUrl = channelUrl+"queryDriverReportInfo";
                break;
            case RabbitConstants.BusiType.WLHY_VEHICLE:
                channelUrl = channelUrl+"queryVehicleReportInfo";
                break;
            case RabbitConstants.BusiType.WLHY_WAYBILL:
                channelUrl = channelUrl+"queryWaybillReportInfo";
                break;
            case RabbitConstants.BusiType.WLHY_ACCOUNT:
                channelUrl = channelUrl+"queryCapitalAccountReportInfo";
                break;
            case RabbitConstants.BusiType.WLHY_CAPITAL_FLOW:
                channelUrl = channelUrl+"queryCapitalFlowReportInfo";
                break;
            default:
                log.info("httpInvokeMappingTarget#没有找到业务类型:{}",message.getBusiType());
                return null;
        }
        return HttpUtil.post(channelUrl, JSON.toJSONString(message));
    }

    /**
     * 调用映射信息
     * @param message
     * @return
     */
    private String feignInvokeMappingTarget(BaseRabbitMessageVO message) {
        switch (message.getBusiType()) {
            case RabbitConstants.BusiType.WLHY_DRIVER:
                return srsExchangeService.queryDriverReportInfo(JSON.toJSONString(message));
            case RabbitConstants.BusiType.WLHY_VEHICLE:
                return srsExchangeService.queryVehicleReportInfo(JSON.toJSONString(message));
            case RabbitConstants.BusiType.WLHY_WAYBILL:
                return srsExchangeService.queryWaybillReportInfo(JSON.toJSONString(message));
            case RabbitConstants.BusiType.WLHY_ACCOUNT:
                return srsExchangeService.queryCapitalFlowReportInfo(JSON.toJSONString(message));
            case RabbitConstants.BusiType.WLHY_CAPITAL_FLOW:
                return srsExchangeService.queryCapitalFlowReportInfo(JSON.toJSONString(message));
            case RabbitConstants.BusiType.WLHY_SHIPPER:
                return srsExchangeService.queryShipperReportInfo(JSON.toJSONString(message));
            case RabbitConstants.BusiType.WLHY_ACTUAL_CARRIER:
                return srsExchangeService.queryActualCarrierReportInfo(JSON.toJSONString(message));
            case RabbitConstants.BusiType.WLHY_CONTRACT:
                return srsExchangeService.queryContractReportInfo(JSON.toJSONString(message));
            default:
                log.info("feignInvokeMappingTarget#没有找到业务类型:{}",message.getBusiType());
                return null;
        }
    }

}
