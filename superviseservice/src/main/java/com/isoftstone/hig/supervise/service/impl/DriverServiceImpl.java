package com.isoftstone.hig.supervise.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.api.entity.Driver;
import com.isoftstone.hig.supervise.api.entity.MessageNote;
import com.isoftstone.hig.supervise.api.entity.ReportMsgVO;
import com.isoftstone.hig.supervise.api.filter.DriverBusSearchVo;
import com.isoftstone.hig.supervise.api.service.*;
import com.isoftstone.hig.supervise.service.rabbitmq.RabbitConstants;
import com.isoftstone.hig.supervise.service.repository.DriverBusinessRepository;
import com.isoftstone.hig.supervise.service.repository.DriverOriginRepository;
import com.isoftstone.hig.supervise.service.repository.MessageNoteRepository;
import com.isoftstone.hig.supervise.service.utils.SpvSpringFactory;
import com.isoftstone.hig.supervise.service.utils.StringUtils;
import com.isoftstone.hig.supervise.service.utils.exception.SupeWlydException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;

@Service
@Slf4j
public class DriverServiceImpl implements DriverService {

    @Resource
    DriverOriginRepository driverOriginRepository;
    @Resource
    DriverBusinessRepository driverBusinessRepository;

    @Resource
    MessageNoteService messageNoteService;
    @Resource
    MessageNoteRepository messageNoteRepository;

    @Resource
    private BusiMsgService busiMsgService;

    @Resource
    private EnumMappingService enumMappingService;
    @Autowired
    private ChannelConfigService channelConfigService;


    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void addDriverInfo(Driver driver) throws Exception{

        //先查询司机是否存在，存在则修改，否则就新增
        Driver resultDriver = this.queryDriverBusInfoByDrivingLicense(driver.getDrivingLicense());
        //设置为待处理状态
        driver.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        if (resultDriver != null) {
            driverOriginRepository.updateDriverOriginInfo(driver);
            //转换从业资格证号
            formatQualificationCertificate(driver);
            driverBusinessRepository.updateDriverBusInfo(driver);

            //删除司机上报记录
            messageNoteService.deleteMessageNoteByMessageContentId(driver.getDriverId(),RabbitConstants.BusiType.WLHY_DRIVER);
        }else{
            driverOriginRepository.addDriverOriginInfo(driver);
            //转换从业资格证号
            formatQualificationCertificate(driver);
            if (StrUtil.isEmpty(driver.getDataAddTag())){
                driver.setDataAddTag("");
            }
            driverBusinessRepository.addDriverBusinessInfo(driver);
        }
    }

    @Override
    public ResultMode<Driver> queryDriverBusInfoPageInfo(PagingInfo<DriverBusSearchVo> pageInfo) throws Exception{
        log.info("进入 DriverServiceImpl.queryDriverBusInfoPageInfo  方法中查询参数{}", JSON.toJSONString(pageInfo));

        //主体条件
        DriverBusSearchVo filterModel = pageInfo.getFilterModel();
        if (StrUtil.isNotEmpty(pageInfo.getFilterModel().getNetworkMainBodyId())) {
            //上报开始时间
            if (StrUtil.isNotEmpty(filterModel.getValidReportStart())) {
                filterModel.setNetValidReportStart(filterModel.getValidReportStart());
                filterModel.setValidReportStart(null);
            }
            //上报结束时间
            if (StrUtil.isNotEmpty(filterModel.getValidReportEnd())) {
                filterModel.setNetValidReportEnd(filterModel.getValidReportEnd());
                filterModel.setValidReportEnd(null);
            }
            int count = driverBusinessRepository.countDriverBusInfoByNetworkIdCondition(pageInfo.getFilterModel());
            if (count == 0) {
                return ResultMode.success();
            }

            PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength, false);
            List<Driver> driverList = driverBusinessRepository.queryDriverBusInfoByNetworkIdCondition(pageInfo.getFilterModel());
            driverList.stream().forEach(driver -> {
                driver.setAgainReport(true);
                driver.setStatus(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode());
                driver.setSendToProDateTime(driver.getMessageTime());
                if (StrUtil.isNotEmpty(driver.getNetworkMainBodyName())) {
                    driver.setSuccessMessage(driver.getNetworkMainBodyName() + "(" + driver.getResultInfo() + ")");
                }
            });
            return ResultMode.successPageList(driverList, count);
        }

        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);

        List<Driver> list = driverBusinessRepository.queryDriverBusInfoBySearchCondition(pageInfo.getFilterModel());
        if (null != list) {
            log.info("进入 driverBusinessRepository.queryDriverBusInfoBySearchCondition方法返回结果{}", JSON.toJSONString(list));
            MessageNote note = null;
            for(Driver driver : list){
                //需要判断是否为多次上报，是的则返回true，否则为false
                note = new MessageNote();
                note.setMessageContentId(driver.getDriverId());
                note.setMessageType("driver");
                List<MessageNote> messageNoteList = messageNoteRepository.queryMessagePageInfo(note);
                if(messageNoteList != null && messageNoteList.size() > 0){
                    driver.setAgainReport(true);
                }
            }
        }
        PageInfo<Driver> returnPageInfo = new PageInfo<Driver>(list);
        ResultMode<Driver> mode = new ResultMode<>();
        mode.setTotal((int) returnPageInfo.getTotal());
        mode.setModel(returnPageInfo.getList());
        return mode;
    }

    @Override
    public List<Driver> exportDriver(DriverBusSearchVo driver) {
        List<Driver> list = driverBusinessRepository.exportDriver(driver);
        if(StringUtils.isEmpty(list)){
            return new ArrayList<>(0);
        }
        for(Driver temp:list){
            if (!StringUtils.isEmpty(temp.getStatus())){
                temp.setStatusType(RabbitConstants.STATUS_NAME_MAP.get(temp.getStatus()));
            }
        }

        return list;
    }

    //逻辑删除  司机信息上报
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public ResultMode<Driver> deleteDriverBusInfo(String driverId) throws Exception{

        ResultMode<Driver> model = new ResultMode<>();

        Driver driver = new Driver();
        driver.setDriverId(driverId);

        //校验数据是否可以删除
        DriverBusSearchVo vo = new DriverBusSearchVo();
        List<String> idList = new ArrayList<String>();
        idList.add(driverId);
        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode());
        vo.setIdList(idList);
        vo.setStatusList(statusList);
        List<Driver> searchResult = searchExistsData(vo);
        if(searchResult != null && searchResult.size() >0){  ///有数据代表着 可以删除
            //设置为删除状态
            driver.setStatus(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
            driver.setModifyBy(JwtUtil.getInstance().getUserBaseIdByToken());
            driver.setModifyDate(DateUtil.date());
            log.info("进入 DriverServiceImpl.deleteDriverBusInfo  方法中查询参数{}", JSON.toJSONString(driverId));
            driverBusinessRepository.updateDriverBusInfo(driver);
        }else{
            model.setErrMsg("该司机信息不能操作！");
            model.setSucceed(false);
        }
        return model;

    }

    //保存业务司机信息
    //保存可以重复保存
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public ResultMode<Driver> saveDriverInfo(Driver driver) throws Exception {

        ResultMode<Driver> model = new ResultMode<>();

        //校验数据是否可以保存
        DriverBusSearchVo vo = new DriverBusSearchVo();
        List<String> idList = new ArrayList<String>();
        idList.add(driver.getDriverId());
        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode());
        //statusList.add(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        vo.setIdList(idList);
        vo.setStatusList(statusList);
        List<Driver> searchResult = searchExistsData(vo);
        if(searchResult != null && searchResult.size() >0){  ///有数据代表着 不能保存

            Driver temp = searchResult.get(0);
            if(temp.getStatus().equals(UtilityEnum.SuperviseStatusEnum.TO_DO.getCode())){
                //设置为保存状态
                driver.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
            }
            log.info("进入 DriverServiceImpl.updateDriverInfo  方法中查询参数{}", JSON.toJSONString(driver));
            saveDriverBusInfo(driver);

        }else{
            model.setErrMsg("该司机信息不能操作！");
            model.setSucceed(false);
        }
        return model;
    }

    //提交业务司机信息
    @Override
    public ResultMode<Driver> submitDriverInfo(Driver driver) throws Exception {
        if(StringUtils.isEmpty(driver.getNetworkMainBodyIdList())){
            return ResultMode.fail("没有上传网络货运主体id");
        }
        Driver temp = driverBusinessRepository.viewDriverBusInfo(driver);
        if(temp==null){
            return ResultMode.fail("上报司机信息["+driver.getDriverId()+"]没有找到");
        }

        ResultMode<Driver> model = new ResultMode<>();
        if(UtilityEnum.SuperviseStatusEnum.DELETE.getCode().equals(temp.getStatus())){
            model.setErrMsg("该司机信息已删除，不能操作！");
            model.setSucceed(false);
            return model;
        }else if(UtilityEnum.SuperviseStatusEnum.TO_DO.getCode().equals(temp.getStatus())){
            model.setErrMsg("该司机信息尚未保存，不能直接提交上报！");
            model.setSucceed(false);
            return model;
        }else if(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode().equals(temp.getStatus())){
            model.setErrMsg("该司机信息已上报成功，不能操作！");
            model.setSucceed(false);
            return model;
        }

        //判断是否有网络主体已在上报中或上报成功
        model =  messageNoteService.checkNetworkMainBodyStatus(temp.getDriverId(),RabbitConstants.BusiType.WLHY_DRIVER, driver.getNetworkMainBodyIdList());
        if(!model.isSucceed()){
            return model;
        }

        //保存
        saveDriverBusInfo(driver);
        BeanUtil.copyProperties(driver, temp, CopyOptions.create().ignoreNullValue().ignoreError());

        //设置网络主体id
        temp.setNetworkMainBodyIdList(driver.getNetworkMainBodyIdList());
        temp.setNetworkMainBodyId(driver.getNetworkMainBodyId());
        model = SpvSpringFactory.getAopProxy(this).report(temp);
        return model;
    }
    @Override
    public void autoReportDriver(List<String> custIds, Integer pageSize) {
        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        custIds.forEach(custId ->{
            List<Driver> driverList = driverBusinessRepository.autoQueryReportDriver(statusList, pageSize, custId);
            driverList = driverList.stream().filter(item -> {
                item.setNetworkMainBodyId(custId);
                return true;
            }).collect(Collectors.toList());
            reportDriverList(driverList);
        });
    }

    @Override
    public void reportDriverList(List<Driver> driverList) {
        if (IterUtil.isEmpty(driverList)) {
            return;
        }
        driverList.forEach(item -> {
            ResultMode<Driver> model = SpvSpringFactory.getAopProxy(this).report(item);
            if (!model.isSucceed()) {
                log.info("该司机信息上报异常！参数：{}，异常信息：{}", JSON.toJSONString(item), model.getErrMsg());
                return;
            }
        });
    }

    /**
     * 保存司机信息
     */
    private void saveDriverBusInfo(Driver driver) {
        //转换从业资格证号
        if (StrUtil.isBlank(driver.getQualificationCertificate())) {
            formatQualificationCertificate(driver);
        }
        driver.setModifyDate(DateUtil.date());
        driver.setModifyBy(JwtUtil.getInstance().getUserBaseIdByToken());
        //更新司机信息
        int update = driverBusinessRepository.updateDriverBusInfo(driver);
        if (update <= 0) {
            log.info("reportAgain#更新司机信息失败，driver：{}", JSONUtil.toJsonStr(driver));
            throw new SupeWlydException("更新司机信息失败");
        }
    }

    @Override
    public ResultMode<Driver> viewDriverBusInfoDetail(String driverId) throws Exception{
        log.info("进入 DriverServiceImpl.viewDriverBusInfoDetail  方法中查询参数{}", JSON.toJSONString(driverId));
        Driver driver = new Driver();
        driver.setDriverId(driverId);
        Driver obj = driverBusinessRepository.viewDriverBusInfo(driver);
        ResultMode<Driver> mode = new ResultMode<>();
        if(null != obj){
            log.info("进入 driverBusinessRepository.viewDriverBusInfoDetail方法返回结果{}", JSON.toJSONString(obj));
            mode.getModel().add(obj);
        }
        return mode;
    }

    @Override
    public ResultMode<Driver> viewDriverRepInfoDetail(String driverId,String networkMainBodyId) throws Exception{
        log.info("进入 DriverServiceImpl.viewDriverRepInfoDetail  方法中查询参数{}", JSON.toJSONString(driverId));
        Driver driver = new Driver();
        driver.setDriverId(driverId);
        Driver obj = driverBusinessRepository.viewDriverBusInfo(driver);
        if(obj==null){
            return ResultMode.success();
        }
        obj.setBusiType(RabbitConstants.BusiType.WLHY_DRIVER);
        obj.setNetworkMainBodyId(networkMainBodyId);
        ResultMode resultMode = busiMsgService.queryMappingResults(obj);
        if(!resultMode.isSucceed()){
            return ResultMode.success();
        }
        obj = (Driver)resultMode.getModel().get(0);
        if(null != obj){
            log.info("进入 driverReportRepository.viewDriverBusInfoDetail方法返回结果{}", JSON.toJSONString(obj));
            //转换准车型名称
            //20191209192510100001:准驾车型
            String vehicleClassName = enumMappingService.getDictNameByCode(obj.getVehicleClass(),"20191209192510100001");
            obj.setVehicleClass(vehicleClassName + "/" + obj.getVehicleClass());
        }
        ResultMode<Driver> mode = new ResultMode<>();
        mode.getModel().add(obj);
        return mode;
    }

    @Override
    public ResultMode<String> batchReportDriver(List<String> driverIdList) throws Exception {
        ResultMode<String> resultModel = new ResultMode<>();

        if(driverIdList == null || driverIdList.size() == 0) {
            resultModel.setErrMsg("driverIdList不能为空");
            resultModel.setSucceed(false);
            return resultModel;
        }

        DriverBusSearchVo vo = new DriverBusSearchVo();
        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        //statusList.add(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode());
        vo.setIdList(driverIdList);
        vo.setStatusList(statusList);
        List<Driver> searchResult = searchExistsData(vo);
        if(searchResult != null && searchResult.size() > 0){    ///有数据代表不能提交
            String msg = "";
            List<String> msgList = new ArrayList<>();
            for (Driver driver : searchResult) {
                msgList.add(driver.getDriverName());
            }
            msg = String.join(",", msgList);
            resultModel.setErrMsg(msg + " 司机信息已经处理，请勿重复处理");
            resultModel.setSucceed(false);
        }else{
            for (String driverId : driverIdList) {
                if(StringUtils.isEmpty(driverId)){
                    continue;
                }
                //上报
                Driver temp = new Driver();
                temp.setDriverId(driverId);
                Driver driver = driverBusinessRepository.viewDriverBusInfo(temp);
                if(driver==null){
                    return ResultMode.fail("上报司机信息["+temp.getDriverId()+"]没有找到");
                }

                resultModel= SpvSpringFactory.getAopProxy(this).report(driver);
                if(!resultModel.isSucceed()){
                    return resultModel;
                }
            }
        }
        return resultModel;
    }

    @Override
    public void updateReportStatus(ReportMsgVO reportMsg) {
        if(StringUtils.isEmpty(reportMsg.getBusiId())){
            return;
        }
        //查询已上报数量
        Driver temp = driverBusinessRepository.queryReportNumById(reportMsg.getBusiId());
        if(temp==null){
            return;
        }

        int sucNum = messageNoteService.countSucReportNum(reportMsg.getBusiId(),reportMsg.getBusiType());
        //需要考虑网络主体是否已经上报过
        int repNum = messageNoteService.checkIsUploadData(reportMsg.getBusiId(),reportMsg.getBusiType(),reportMsg.getNetworkMainBodyId());
        if(reportMsg.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode())){
            if(repNum==0){
                sucNum = sucNum + 1;
            }
        }else{
            if(repNum>0){
                sucNum = sucNum - 1;
            }
        }
        temp.setFinNum(sucNum);
        temp.setReportNum(channelConfigService.getChannelNum());

        Driver driver = new Driver();
        driver.setDriverId(reportMsg.getBusiId());
        driver.setSendToProDateTime(reportMsg.getReportTime());
        driver.setModifyDate(new Date());
        driver.setFinNum(temp.getFinNum());
        driver.setReportNum(temp.getReportNum());
        driver.setStatus(getStatus(reportMsg,temp));
        //网络货运主体企业简称(code：message)
        driver.setSuccessMessage(reportMsg.getNetworkMainBodyName()+"("+reportMsg.getResultInfo()+")");
        driver.setSuccessMessage(StringUtils.substring(driver.getSuccessMessage(),0,255));
        driverBusinessRepository.updateReportStatusOfCompleted(driver);
    }

    private String getStatus(ReportMsgVO reportMsg,Driver driver){
//        if(driver.getReportNum().intValue()<=driver.getFinNum().intValue()){
//            //上报完成
//            return UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode();
//        }else{
            //多渠道只要有一个上报失败，状态为上报失败
            if(driver.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode()) ||
                reportMsg.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode())){
                return UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode();
            }

            //多渠道时，只要有没有返回结果的，都为上报中状态
            return UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode();
//        }
    }


    @Override
    public Driver queryDriverBusInfoByDrivingLicense(String drivingLicense) {
        if(StringUtils.isEmpty(drivingLicense)){
            return null;
        }
        Driver driver = new Driver();
        driver.setDrivingLicense(drivingLicense);
        //默认取第一条
        PageHelper.startPage(0, 1, false);
        return driverBusinessRepository.viewDriverBusInfo(driver);
    }

    @Override
    public List<Driver> queryReportDriverData(Integer start, Integer end, Map<String,Object> paramMap) {
        List<String> statusList = (List<String>)paramMap.get("status");
        return driverBusinessRepository.queryReportDriverData(start,end,statusList);
    }

    /**
     * 上报监管平台
     * @return
     * @throws JAXBException
     */
    @Transactional(rollbackFor = {Exception.class})
    public ResultMode report(Driver driver){
        driver.setBusiType(RabbitConstants.BusiType.WLHY_DRIVER);
        driver.setBusiId(driver.getDriverId());

        //设置网络货运主体
        ResultMode resultMode = busiMsgService.setNetworkId(driver);
        if(!resultMode.isSucceed()){
            return resultMode;
        }

        driver.setStatus(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        driver.setModifyDate(new Date());
        driver.setFinNum(0);
        String userBaseId = StrUtil.blankToDefault(JwtUtil.getInstance().getUserBaseIdByToken(), "");
        driver.setModifyBy(userBaseId);
        driver.setReporterId(userBaseId);
        driverBusinessRepository.updateReportingStatus(driver);

        resultMode =  busiMsgService.convertAndSend(driver);
        return resultMode;
    }

    /**
     * searchExistsData:        检验是否重复操作
     * @param vo
     * @return
     */
    private List<Driver> searchExistsData(DriverBusSearchVo vo){
        log.info("进入 DriverServiceImpl.searchExistsData  方法中查询参数{}", JSON.toJSONString(vo));
        log.info("检查开始。。。。。");
        List<Driver> result = driverBusinessRepository.searchExistsData(vo);
        if(result != null && result.size() >0){
            return result;
        }
        return null;
    }

    /**
     * 查询准车型名称
     * @param code
     * @return
     */
 /*   private String getVehicleClassByKey(String code){
        PagingInfo<PlatformCmDictionaryFilter> pageInfo = new PagingInfo<>();
        PlatformCmDictionaryFilter filter = new PlatformCmDictionaryFilter();
        filter.setEnumCode(code);
        filter.setParentdicId("20191209192510100001");
        pageInfo.setFilterModel(filter);
        ResultMode<PlatformCmDictionary> dictionaryResultMode = platformCmDictionaryInter.platformCmDictionaryAllPaging(pageInfo);
        if(dictionaryResultMode != null && dictionaryResultMode.getModel().size() > 0){
            PlatformCmDictionary dictionary = dictionaryResultMode.getModel().get(0);
            return dictionary.getName();
        }
        return "";
    }
*/


    /**
     * 转换从业资格证号
     * @param driver
     */
    private void formatQualificationCertificate(Driver driver){
        //20191209192510100001:准驾车型
        String vehicleClassName = enumMappingService.getDictNameByCode(driver.getVehicleClass(),"20191209192510100001");
        //只有含有A或B的（如B1F、A2D、A1、B2等传从业资格证号）传资格证号，其他不含A或B的传身份证前六位+12个0
        if(!(vehicleClassName.contains("A") || vehicleClassName.contains("B"))){
            driver.setQualificationCertificate(driver.getDrivingLicense().substring(0,6) + "000000000000");
        }
    }

    /**
     * 司机信息重新上报-保存并上报
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultMode reportAgain(Driver driver) {
        //参数校验
        ResultMode resultMode = reportAgainCheck(driver);
        if (!resultMode.isSucceed()) {
            return resultMode;
        }

        //更新司机信息
        saveDriverBusInfo(driver);

        Driver temp = driverBusinessRepository.viewDriverBusInfo(driver);
        if (temp == null) {
            throw new SupeWlydException("上报司机信息[" + driver.getDriverId() + "]没有找到");
        }
        temp.setNetworkMainBodyIdList(driver.getNetworkMainBodyIdList());
        //上报
        resultMode = SpvSpringFactory.getAopProxy(this).report(temp);

        return resultMode;
    }

    /**
     * 司机信息重新上报-参数校验
     */
    private ResultMode reportAgainCheck(Driver driver) {
        if (null == driver) {
            return ResultMode.fail("司机信息不允许为空");
        }
        if (StrUtil.isBlank(driver.getDriverId())) {
            return ResultMode.fail("司机Id不允许为空");
        }
        if (IterUtil.isEmpty(driver.getNetworkMainBodyIdList())) {
            return ResultMode.fail("网络货运主体Id不允许为空");
        }

        for (String mainId : driver.getNetworkMainBodyIdList()) {
            //上报成功 才允许重新上报
            int count = messageNoteService.checkIsUploadData(driver.getDriverId(), RabbitConstants.BusiType.WLHY_DRIVER, mainId);
            if (count == 0) {
                return ResultMode.fail("司机[" + driver.getDriverName() + "]未上报成功，不支持重新上报");
            }
        }

        return ResultMode.success();
    }

}
