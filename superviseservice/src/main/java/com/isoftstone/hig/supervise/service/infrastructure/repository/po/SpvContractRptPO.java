package com.isoftstone.hig.supervise.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 上报合同信息实体类
 */
@Data
@TableName("spv_contract_rpt")
public class SpvContractRptPO {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 合同编号
     */
    @TableField("contract_no")
    private String contractNo;

    /**
     * 合同类型[1托运合同 2承运合同]
     */
    @TableField("contract_type")
    private Integer contractType;

    /**
     * 甲方
     */
    @TableField("partya")
    private String partyA;

    /**
     * 乙方
     */
    @TableField("partyb")
    private String partyB;

    /**
     * 合同金额
     */
    @TableField("contract_amount")
    private BigDecimal contractAmount;

    /**
     * 合同签订时间
     */
    @TableField("contract_sign_time")
    private Date contractSignTime;

    /**
     * 合同照片访问地址
     */
    @TableField("contract_photo_access_url")
    private String contractPhotoAccessUrl;

    /**
     * 有效期起
     */
    @TableField("valid_start_date")
    private Date validStartDate;

    /**
     * 有效期止
     */
    @TableField("valid_end_date")
    private Date validEndDate;

    /**
     * 网络货运主体id
     */
    @TableField("network_main_body_id")
    private String networkMainBodyId;

    /**
     * 网络货运主体名称
     */
    @TableField("network_main_body_name")
    private String networkMainBodyName;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 状态[4上报成功 5上报失败]
     */
    @TableField("status")
    private String status;

    /**
     * 上报时间
     */
    @TableField("send_to_pro_date_time")
    private Date sendToProDateTime;

    /**
     * 返回结果
     */
    @TableField("result_info")
    private String resultInfo;

    /**
     * 创建人id
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 最后更新人id
     */
    @TableField("last_updater_id")
    private String lastUpdaterId;

    /**
     * 最后更新时间
     */
    @TableField(value = "last_update_time")
    private Date lastUpdateTime;
}
