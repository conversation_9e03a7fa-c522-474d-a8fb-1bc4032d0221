package com.isoftstone.hig.supervise.service.domain.model.entity;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 合同信息领域实体
 */
@Data
public class SpvContractBusInfoEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同类型[1托运合同 2承运合同]
     */
    private Integer contractType;

    /**
     * 甲方
     */
    private String partyA;

    /**
     * 乙方
     */
    private String partyB;

    /**
     * 合同金额
     */
    private BigDecimal contractAmount;

    /**
     * 合同签订时间
     */
    private Date contractSignTime;

    /**
     * 合同照片访问地址
     */
    private String contractPhotoAccessUrl;

    /**
     * 有效期起
     */
    private Date validStartDate;

    /**
     * 有效期止
     */
    private Date validEndDate;

    /**
     * 上报时间
     */
    private Date sendToProDateTime;

    /**
     * 状态[0删除 1待处理 2已保存 3待上报 4上报成功 5上报失败]
     */
    private String status;

    /**
     * 返回消息
     */
    private String successMessage;

    /**
     * 上报人id
     */
    private String reporterId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人id
     */
    private String creatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新人id
     */
    private String lastUpdaterId;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;
}
