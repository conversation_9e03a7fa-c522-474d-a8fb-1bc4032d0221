package com.isoftstone.hig.supervise.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.api.entity.*;
import com.isoftstone.hig.supervise.api.filter.VehicleBusSearchVo;
import com.isoftstone.hig.supervise.api.service.*;
import com.isoftstone.hig.supervise.service.enmus.SuperviseEnmus;
import com.isoftstone.hig.supervise.service.infrastructure.repository.mapper.SpvRangeEnumMappingMapper;
import com.isoftstone.hig.supervise.service.infrastructure.repository.po.SpvRangeEnumMappingPO;
import com.isoftstone.hig.supervise.service.rabbitmq.RabbitConstants;
import com.isoftstone.hig.supervise.service.repository.*;
import com.isoftstone.hig.supervise.service.utils.IdUtil;
import com.isoftstone.hig.supervise.service.utils.SpvSpringFactory;
import com.isoftstone.hig.supervise.service.utils.StringUtils;
import com.isoftstone.hig.supervise.service.utils.exception.SupeWlydException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;

@Service
@Slf4j
public class VehicleServiceImpl implements VehicleService {

    @Resource
    VehicleBusinessRepository vehicleBusinessRepository;
    @Resource
    VehicleOriginRepository vehicleOriginRepository;

    @Resource
    EnumMappingRepository enumMappingRepository;
    @Resource
    MessageNoteService messageNoteService;
    @Resource
    MessageNoteRepository messageNoteRepository;
    @Resource
    private EnumMappingService enumMappingService;

    @Resource
    private BusiMsgService busiMsgService;

    @Autowired
    private ChannelConfigService channelConfigService;

    @Resource
    private WaybillAttributeBusinessRepository waybillAttributeBusinessRepository;

    @Resource
    private CapitalAccountBusinessRepository capitalAccountBusinessRepository;

    @Resource
    private SuperviseCapitalFlowMapper superviseCapitalFlowMapper;

    @Resource
    private SuperviseCapitalFlowWaybillMapper superviseCapitalFlowWaybillMapper;

    @Resource
    private Executor asyncServiceExecutor;

    @Resource
    private SpvRangeEnumMappingMapper rangeEnumMappingMapper;

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void addVehicleInfo(Vehicle vehicle) throws Exception{
        //千克转换为吨
        formatWeight(vehicle);
        //车辆轴数处理通过vehicleTonnage字段转换
        log.info("addVehicleInfo#核定载质量：{}", vehicle.getVehicleTonnage());
        String vehicleTonnage = vehicle.getVehicleTonnage();
        SpvRangeEnumMappingPO rangeEnumMappingPO = rangeEnumMappingMapper.selectByVehicleTonnage(vehicleTonnage);
        if (ObjectUtil.isNotEmpty(rangeEnumMappingPO)) {
            vehicle.setAxlenum(rangeEnumMappingPO.getMappingValue());
        }
        //根据车牌号查是否存在
        Vehicle vehicleFilter = new Vehicle();
        vehicleFilter.setVehicleNumber(vehicle.getVehicleNumber());
        Vehicle oldVehicle = vehicleBusinessRepository.queryVehicleBusInfo(vehicleFilter);
        //设置为待处理状态
        vehicle.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        // 挂车车牌号赋值
        vehicle.setTrailerVehiclePlateNumber(vehicle.getTrailerPlateNo());
        //如果对应的车辆存在，则进行修改，否则新增
        if(oldVehicle != null){
            vehicle.setId(oldVehicle.getId());
            vehicleOriginRepository.update(vehicle);
            vehicleBusinessRepository.update(vehicle);

            //删除车辆上报记录
            messageNoteService.deleteMessageNoteByMessageContentId(String.valueOf(oldVehicle.getId()),RabbitConstants.BusiType.WLHY_VEHICLE);

            //车牌颜色变更，同步更新运单和资金
            asyncServiceExecutor.execute(() -> updateVehiclePlateColor(oldVehicle, vehicle));

        }else{
            Long id = IdUtil.generateId();
            vehicle.setId(String.valueOf(id));
            //挂车数据处理
            formatTrailer(vehicle);
            if (StrUtil.isEmpty(vehicle.getDataAddTag())){
                vehicle.setDataAddTag("");
            }
            vehicleOriginRepository.addVehicleInfo(vehicle);
            vehicleBusinessRepository.addVehicleInfo(vehicle);

        }
    }

    /**
     * 车牌颜色变更，同步更新运单和资金
     */
    public void updateVehiclePlateColor(Vehicle oldVehicle, Vehicle vehicle) {
        if (StrUtil.isBlank(vehicle.getVehiclePlateColorCode())
            || StrUtil.equals(oldVehicle.getVehiclePlateColorCode(), vehicle.getVehiclePlateColorCode())) {
            return;
        }

        Vehicle vehicleUpd = new Vehicle();
        if (StrUtil.equals(vehicle.getCarType(), SuperviseEnmus.CarTypeEnum.CAR_TYPE_TRAILER.getCode())) {
            //挂车-只修改运单
            vehicleUpd.setTrailerVehiclePlateNumber(vehicle.getVehicleNumber());
            vehicleUpd.setTrailerVehiclePlateColorCode(vehicle.getVehiclePlateColorCode());
            log.info("addVehicleInfo#挂车车牌颜色变更，同步更新运单和资金,：{}", JSONUtil.toJsonStr(vehicleUpd));
            waybillAttributeBusinessRepository.updateTrailerPlateColor(vehicleUpd);
            return;
        }

        //车辆-修改未上报运单/资金，根据运单改资金
        List<String> waybillIdList = waybillAttributeBusinessRepository.queryUnReportByVehicleNumber(vehicle.getVehicleNumber());
        if (IterUtil.isEmpty(waybillIdList)) {
            return;
        }

        //过滤toB资金中，已上报的运单
        List<String> reportWaybillIdList = superviseCapitalFlowWaybillMapper.queryExistRelationWaybills(waybillIdList);
        if (IterUtil.isNotEmpty(reportWaybillIdList)) {
            waybillIdList.removeAll(reportWaybillIdList);
            if (IterUtil.isEmpty(waybillIdList)) {
                return;
            }
        }
        log.info("addVehicleInfo#车牌颜色变更，同步更新运单和资金,waybillIdList：{}, 车牌号:{}, 车牌颜色:{}",
            JSONUtil.toJsonStr(waybillIdList), vehicle.getVehicleNumber(), vehicle.getVehiclePlateColorCode());

        waybillAttributeBusinessRepository.updateVehiclePlateColorByWaybillIds(waybillIdList, vehicle.getVehiclePlateColorCode());
        capitalAccountBusinessRepository.updateVehiclePlateColorByWaybillIds(waybillIdList, vehicle.getVehiclePlateColorCode());
        superviseCapitalFlowMapper.updateVehiclePlateColorByWaybillIds(waybillIdList, vehicle.getVehiclePlateColorCode());
    }

    /**
     * 挂车数据处理
     *
     * @param trailer
     * @return void
     * <AUTHOR>
     * @Date 2023/7/20 11:40
     */
    private void formatTrailer(Vehicle trailer) {
        // 挂车类型 且 道路运输证号没有值
        String trailerVehicleNumber = trailer.getVehicleNumber();
        if (StrUtil.equals(trailer.getCarType(), SuperviseEnmus.CarTypeEnum.CAR_TYPE_TRAILER.getCode())
            && StrUtil.isBlank(trailer.getRoadTransportCertificateNumber())
            && StrUtil.isNotBlank(trailerVehicleNumber)) {
            // 挂车没有道路运输证，取默认牵引车的道路运输证号
            PagingInfo<VehicleBusSearchVo> pageInfo = new PagingInfo<>();
            VehicleBusSearchVo vehicleBusSearchVo = new VehicleBusSearchVo();
            vehicleBusSearchVo.setTrailerVehiclePlateNumber(trailerVehicleNumber);
            vehicleBusSearchVo.setVehiclePlateColorCode(trailer.getVehiclePlateColorCode());
            pageInfo.setFilterModel(vehicleBusSearchVo);
            pageInfo.setPageLength(1);

            PageHelper.startPage(pageInfo.getCurrentPage(), pageInfo.getPageLength(), pageInfo.getCountTotal());
            List<Vehicle> vehicleList = vehicleBusinessRepository.queryVehicleBusInfoBySearchCondition(pageInfo.getFilterModel());
            log.info("查询挂车{}关联牵引车的信息====》{}", trailerVehicleNumber, JSONUtil.toJsonStr(vehicleList));

            if (IterUtil.isNotEmpty(vehicleList) && ObjectUtil.isNotEmpty(vehicleList.get(0))) {
                Vehicle vehicle = vehicleList.get(0);
                trailer.setRoadTransportCertificateNumber(vehicle.getRoadTransportCertificateNumber());
            }
            // 牵引车没取到，取12位随机数
            if (StrUtil.isBlank(trailer.getRoadTransportCertificateNumber())) {
                trailer.setRoadTransportCertificateNumber(getRandomQualificationNumber(12));
            }
        }

        if (StrUtil.equals(trailer.getCarType(), SuperviseEnmus.CarTypeEnum.CAR_TYPE_TRAILER.getCode())
            && StrUtil.isBlank(trailer.getUseCharacter())) {
            trailer.setUseCharacter("A050");
        }
    }

    /**
     * 随机生成道路运输证
     *
     * @param length
     * @return java.lang.String
     * <AUTHOR>
     * @Date 2023/7/19 13:10
     */
    private static String getRandomQualificationNumber(int length) {
        log.info("PlatformUmAptitudeTrailerDetailBusiness#getRandomQualificationNumber 入参：{}", length);
        //定义一个长度为12的char[]数组,用于存储每一位,其中数组下标为0就代表第一位
        char[] numbers = new char[length];
        //数字0-9之间对应的ascil值区间为[48,57],其中数字0为48
        for (int i = 0; i < numbers.length; i++) {
            if (i == 0) {
                numbers[i] = (char) (RandomUtil.randomInt(9) + 49);
            } else {
                numbers[i] = (char) (RandomUtil.randomInt(10) + 48);
            }
        }
        String QualificationNumber = new String(numbers);
        log.info("PlatformUmAptitudeTrailerDetailBusiness#getRandomQualificationNumber 出参：{}", QualificationNumber);
        return QualificationNumber;
    }

    @Override
    public ResultMode<Vehicle> qureyVehicleBusPageInfo(PagingInfo<VehicleBusSearchVo> pageInfo) throws Exception{
        log.info("进入 VehicleServiceImpl.queryDriverBusInfoPageInfo  方法中查询参数{}", JSON.toJSONString(pageInfo));

        //主体条件
        if (StrUtil.isNotEmpty(pageInfo.getFilterModel().getNetworkMainBodyId())) {
            int count = vehicleBusinessRepository.countVehicleBusInfoByNetworkIdCondition(pageInfo.getFilterModel());
            if (count == 0) {
                return ResultMode.success();
            }

            PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength, false);
            List<Vehicle> vehicleList = vehicleBusinessRepository.queryVehicleBusInfoByNetworkIdCondition(pageInfo.getFilterModel());
            vehicleList.stream().forEach(vehicle -> vehicle.setAgainReport(true));
            return ResultMode.successPageList(vehicleList, count);
        }

        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);

        List<Vehicle> list = vehicleBusinessRepository.queryVehicleBusInfoBySearchCondition(pageInfo.getFilterModel());
        if (null != list) {
            MessageNote note = null;
            for(Vehicle vehicle : list){
                //需要判断是否为多次上报，是的则返回true，否则为false
                note = new MessageNote();
                note.setMessageContentId(String.valueOf(vehicle.getId()));
                note.setMessageType("vehicle");
                List<MessageNote> messageNoteList = messageNoteRepository.queryMessagePageInfo(note);
                if(messageNoteList != null && messageNoteList.size() > 0){
                    vehicle.setAgainReport(true);
                }
            }
        }
        PageInfo<Vehicle> returnPageInfo = new PageInfo<Vehicle>(list);
        ResultMode<Vehicle> mode = new ResultMode<>();
        mode.setTotal((int) returnPageInfo.getTotal());
        mode.setModel(returnPageInfo.getList());
        return mode;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public ResultMode<Vehicle> deleteVehicleInfo(String id) throws Exception {

        ResultMode<Vehicle> model = new ResultMode<>();

        Vehicle vehicle = new Vehicle();
        vehicle.setId(id);

        //校验
        VehicleBusSearchVo vo = new VehicleBusSearchVo();
        List<String> idList = new ArrayList<String>();
        idList.add(String.valueOf(id));
        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode());
        vo.setIdList(idList);
        vo.setStatusList(statusList);
        List<Vehicle> searchResult = searchExistsData(vo);
        if(searchResult != null && searchResult.size() >0){
            //设置为删除状态
            vehicle.setStatus(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
            vehicle.setModifyBy(JwtUtil.getInstance().getUserBaseIdByToken());
            vehicle.setModifyDate(DateUtil.date());
            log.info("进入 VehicleServiceImpl.deleteVehicleInfo  方法中参数{}", JSON.toJSONString(vehicle));
            vehicleBusinessRepository.update(vehicle);
        }else{
            model.setErrMsg("该车辆信息不能操作！");
            model.setSucceed(false);
        }
        return model;
    }

    //保存可以重复保存
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public ResultMode<Vehicle> saveVehicleInfo(Vehicle vehicle) throws Exception {

        ResultMode<Vehicle> model = new ResultMode<>();

        //校验
        VehicleBusSearchVo vo = new VehicleBusSearchVo();
        List<String> idList = new ArrayList<String>();
        idList.add(String.valueOf(vehicle.getId()));
        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode());
        //statusList.add(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        vo.setIdList(idList);
        vo.setStatusList(statusList);
        List<Vehicle> searchResult = searchExistsData(vo);
        if(searchResult != null && searchResult.size() >0){
            Vehicle temp = searchResult.get(0);
            if(temp.getStatus().equals(UtilityEnum.SuperviseStatusEnum.TO_DO.getCode())){
                //设置为保存状态
                vehicle.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
            }
            log.info("进入 VehicleServiceImpl.saveVehicleInfo  方法中参数{}", JSON.toJSONString(vehicle));
            //保存车辆信息
            saveVehicleBusInfo(vehicle);
        }else{
            model.setErrMsg("该车辆信息不能操作！");
            model.setSucceed(false);
        }

        return model;
    }

    @Override
    public ResultMode<Vehicle> submitVehicle(Vehicle vehicle) throws Exception {
        if (StringUtils.isEmpty(vehicle.getNetworkMainBodyIdList())) {
            return ResultMode.fail("没有上传网络货运主体id");
        }
        Vehicle result = vehicleBusinessRepository.queryVehicleBusInfoById(vehicle.getId());
        if (result == null) {
            return ResultMode.fail("上报车辆信息[" + vehicle.getId() + "]没有找到");
        }

        result.setNetworkMainBodyIdList(vehicle.getNetworkMainBodyIdList());
        result.setNetworkMainBodyId(vehicle.getNetworkMainBodyId());
        // 车辆上报校验
        ResultMode<Vehicle> model = checkVehicleReport(result);
        if (!model.isSucceed()) {
            return model;
        }

        //保存车辆信息
        saveVehicleBusInfo(vehicle);
        BeanUtil.copyProperties(vehicle, result, CopyOptions.create().ignoreNullValue().ignoreError());

        model = SpvSpringFactory.getAopProxy(this).report(result);

        //自动上报默认挂车
//        if (ObjectUtil.isNotEmpty(model) && model.isSucceed() && StrUtil.isNotBlank(result.getTrailerVehiclePlateNumber())) {
//            log.info("VehicleServiceImpl#submitVehicle，牵引车[{}]自动上报挂车[{}]信息", result.getVehicleNumber(), result.getTrailerVehiclePlateNumber());
//            ResultMode resultMode = reportTrailerByTractor(result);
//            log.info("VehicleServiceImpl#submitVehicle，牵引车[{}]自动上报挂车[{}]结果：{}"
//                , result.getVehicleNumber(), result.getTrailerVehiclePlateNumber(), JSONUtil.toJsonStr(resultMode));
//        }

        return model;
    }

    /**
     * 保存车辆信息
     */
    private void saveVehicleBusInfo(Vehicle vehicle) {
        vehicle.setModifyDate(DateUtil.date());
        vehicle.setModifyBy(JwtUtil.getInstance().getUserBaseIdByToken());
        //更新车辆信息
        int update = vehicleBusinessRepository.update(vehicle);
        if (update <= 0) {
            log.info("reportAgain#更新车辆信息失败，driver：{}", JSONUtil.toJsonStr(vehicle));
            throw new SupeWlydException("更新车辆信息失败");
        }
    }

    /**
     * 车辆上报校验
     *
     * @param vehicle
     * @return com.isoftstone.hig.common.model.ResultMode<com.isoftstone.hig.supervise.api.entity.Vehicle>
     * <AUTHOR>
     * @Date 2023/7/17 17:46
     */
    private ResultMode checkVehicleReport(Vehicle vehicle) {
        ResultMode model = new ResultMode<>();
        if (UtilityEnum.SuperviseStatusEnum.DELETE.getCode().equals(vehicle.getStatus())) {
            model.setErrMsg("该车辆信息已删除，不能操作！");
            model.setSucceed(false);
            return model;
        } else if (UtilityEnum.SuperviseStatusEnum.TO_DO.getCode().equals(vehicle.getStatus())) {
            model.setErrMsg("该车辆信息尚未保存，不能直接提交上报！");
            model.setSucceed(false);
            return model;
        } else if (UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode().equals(vehicle.getStatus())) {
            model.setErrMsg("该车辆信息已上报成功，不能操作！");
            model.setSucceed(false);
            return model;
        }

        //挂车校验上报渠道
        model = checkReportTrailerChannel(vehicle);
        if (!model.isSucceed()) {
            return model;
        }

        //判断是否有网络主体已在上报中或上报成功
        model = messageNoteService.checkNetworkMainBodyStatus(String.valueOf(vehicle.getId()), RabbitConstants.BusiType.WLHY_VEHICLE, vehicle.getNetworkMainBodyIdList());
        if (!model.isSucceed()) {
            return model;
        }

        return ResultMode.success();
    }

    @Override
    public ResultMode<Vehicle> viewVehicleBusDetailById(String id){
        log.info("进入 VehicleServiceImpl.viewVehicleBusDetail  方法中查询参数{}", id);
        Vehicle obj = vehicleBusinessRepository.queryVehicleBusInfoById(id);
        ResultMode<Vehicle> mode = new ResultMode<>();
        mode.getModel().add(obj);
        return mode;
    }

    @Override
    public ResultMode<Vehicle> viewVehicleBusDetail(String vehicleNumber, String vehiclePlateColorCode) throws Exception{
        log.info("进入 VehicleServiceImpl.viewVehicleBusDetail  方法中查询参数{}", JSON.toJSONString(vehicleNumber));
        Vehicle vehicle = new Vehicle();
        vehicle.setVehicleNumber(vehicleNumber);
        vehicle.setVehiclePlateColorCode(vehiclePlateColorCode);
        Vehicle obj = vehicleBusinessRepository.queryVehicleBusInfo(vehicle);
        ResultMode<Vehicle> mode = new ResultMode<>();
        mode.getModel().add(obj);
        return mode;
    }

    @Override
    public ResultMode<Vehicle> viewVhicleReportDetail(String id,String networkMainBodyId) throws Exception{
        log.info("进入 VehicleServiceImpl.viewVehicleBusDetail  方法中查询参数{}", id);
        Vehicle obj = vehicleBusinessRepository.queryVehicleBusInfoById(id);
        if(obj==null){
            return ResultMode.success();
        }
        obj.setBusiId(String.valueOf(id));
        obj.setBusiType(RabbitConstants.BusiType.WLHY_VEHICLE);
        obj.setNetworkMainBodyId(networkMainBodyId);
        ResultMode resultMode = busiMsgService.queryMappingResults(obj);
        if(!resultMode.isSucceed()){
            return ResultMode.success();
        }
        obj = (Vehicle)resultMode.getModel().get(0);
        if(null != obj){
            log.info("进入 vehicleReportRepository.queryVehicleReportDetail方法返回结果{}", JSON.toJSONString(obj));
            //formatEnumName(obj);
        }
        ResultMode<Vehicle> mode = new ResultMode<>();
        mode.getModel().add(obj);
        return mode;
    }

    @Override
    public ResultMode<String> batchReportVehicle(List<String> vehicleIdList) throws Exception {
        ResultMode<String> resultModel = new ResultMode<>();

        if (IterUtil.isEmpty(vehicleIdList)){
            resultModel.setErrMsg("vehicleIdList不能为空");
            resultModel.setSucceed(false);
            return resultModel;
        }

        //校验
        VehicleBusSearchVo vo = new VehicleBusSearchVo();
        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        //statusList.add(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode());
        vo.setIdList(vehicleIdList);
        vo.setStatusList(statusList);
        List<Vehicle> searchResult = searchExistsData(vo);
        if(searchResult != null && searchResult.size() >0){
            String msg = "";
            List<String> msgList = new ArrayList<>();
            for (Vehicle vehi : searchResult) {
                msgList.add(vehi.getVehicleNumber());
            }
            msg = String.join(",", msgList);
            resultModel.setErrMsg(msg + " 车辆信息已经处理，请勿重复处理");
            resultModel.setSucceed(false);

        }else{
            for (String id : vehicleIdList) {
                Vehicle temp = new Vehicle();
                temp.setId(id);
                Vehicle vehicle = vehicleBusinessRepository.queryVehicleBusInfoById(id);
                if(vehicle==null){
                    return ResultMode.fail("上报车辆信息 id：["+temp.getId()+"]没有找到");
                }

                // 挂车-校验是否需要上报
                resultModel = checkReportTrailerChannel(vehicle);
                if(!resultModel.isSucceed()){
                    return resultModel;
                }

                resultModel =  SpvSpringFactory.getAopProxy(this).report(vehicle);
                if(!resultModel.isSucceed()){
                    return resultModel;
                }

                //自动上报默认挂车
//                if (StrUtil.isNotBlank(vehicle.getTrailerVehiclePlateNumber())) {
//                    log.info("VehicleServiceImpl#batchReportVehicle，牵引车[{}]自动上报挂车[{}]信息", vehicle.getVehicleNumber(), vehicle.getTrailerVehiclePlateNumber());
//                    ResultMode resultMode = reportTrailerByTractor(vehicle);
//                    log.info("VehicleServiceImpl#batchReportVehicle，牵引车[{}]自动上报挂车[{}]结果：{}"
//                        , vehicle.getVehicleNumber(), vehicle.getTrailerVehiclePlateNumber(), JSONUtil.toJsonStr(resultMode));
//                }
            }
        }

        return resultModel;
    }

    /**
     *  挂车-校验是否需要上报
     *
     * @param trailer
     * @return com.isoftstone.hig.common.model.ResultMode<java.lang.String>
     * <AUTHOR>
     * @Date 2023/7/21 15:41
     */
    private ResultMode checkReportTrailerChannel(Vehicle trailer) {
        List<String> trailerCustIds = new ArrayList<>();
        if (StrUtil.endWith(trailer.getVehicleNumber(), "挂")) {
            if (IterUtil.isEmpty(trailer.getNetworkMainBodyIdList()) && StrUtil.isBlank(trailer.getNetworkMainBodyId())) {
                trailerCustIds = channelConfigService.getDefaultTrailerCustIds();
            } else {
                Set<String> networkIdSet = new HashSet<>();
                if(IterUtil.isNotEmpty(trailer.getNetworkMainBodyIdList())){
                    networkIdSet.addAll(trailer.getNetworkMainBodyIdList());
                }
                if(StrUtil.isNotEmpty(trailer.getNetworkMainBodyId())){
                    networkIdSet.add(trailer.getNetworkMainBodyId());
                }
                for (String id: networkIdSet) {
                    SuperviseChannelConfig config = channelConfigService.getConfigByCustId(id);
                    if (ObjectUtil.isNotEmpty(config) && config.getReportTrailer() == 1) {
                        trailerCustIds.add(id);
                    }
                }
            }
            if (IterUtil.isEmpty(trailerCustIds)) {
                return ResultMode.fail("挂车["+ trailer.getVehicleNumber() +"]没有可上报渠道，不允许上报");
            }

            trailer.setNetworkMainBodyIdList(trailerCustIds);
        }

        return ResultMode.success();
    }

    @Override
    public void updateReportStatus(ReportMsgVO reportMsg) {
        if(StringUtils.isEmpty(reportMsg.getBusiId())){
            return;
        }
        // 查询已上报数量
        Vehicle temp = vehicleBusinessRepository.queryVehicleBusInfoById(reportMsg.getBusiId());
        if (temp == null) {
            log.info("车辆不存在，查询参数：{}", JSON.toJSONString(reportMsg));
            return;
        }
        int sucNum = messageNoteService.countSucReportNum(String.valueOf(temp.getId()), reportMsg.getBusiType());
        //需要考虑网络主体是否已经上报过
        int repNum = messageNoteService.checkIsUploadData(String.valueOf(temp.getId()),reportMsg.getBusiType(),reportMsg.getNetworkMainBodyId());
        if(reportMsg.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode())){
            if(repNum==0){
                sucNum = sucNum + 1;
            }
        }else{
            if(repNum>0){
                sucNum = sucNum - 1;
            }
        }
        temp.setFinNum(sucNum);
        temp.setReportNum(channelConfigService.getChannelNum());

        Vehicle vehicle = new Vehicle();
        vehicle.setId(reportMsg.getBusiId());
        vehicle.setSendToProDateTime(reportMsg.getReportTime());
        vehicle.setStatus(reportMsg.getStatus());
        vehicle.setModifyDate(new Date());
        vehicle.setFinNum(temp.getFinNum());
        vehicle.setReportNum(temp.getReportNum());
        vehicle.setStatus(getStatus(reportMsg,temp));
        //网络货运主体企业简称(code：message)
        vehicle.setSuccessMessage(reportMsg.getNetworkMainBodyName()+"("+reportMsg.getResultInfo()+")");
        vehicle.setSuccessMessage(StringUtils.substring(vehicle.getSuccessMessage(),0,255));
        vehicleBusinessRepository.updateReportStatusOfCompleted(vehicle);
    }

    @Override
    public List<Vehicle> exportVehicle(VehicleBusSearchVo vehicle) {
        List<Vehicle> list = vehicleBusinessRepository.exportVehicle(vehicle);
        if(StringUtils.isEmpty(list)){
            return new ArrayList<>(0);
        }
        //267:颜色，223：能源类型，20200304110857100003：车管所车辆类型，20200727102223100001：上报状态
        List<String> dictIdList = Arrays.asList("267","20200304110857100003","20200727102223100001","223");
        Map<String, Map<String,String>> dictMap = enumMappingService.getDictByIds(dictIdList);
        for(Vehicle temp:list){
            if (!StringUtils.isEmpty(temp.getStatus())){
                temp.setStatusType(RabbitConstants.STATUS_NAME_MAP.get(temp.getStatus()));
            }

            if(!StringUtils.isEmpty(temp.getVehiclePlateColorCode()) && dictMap.containsKey("267")){
                //车牌颜色代码
                temp.setVehiclePlateColorCode(dictMap.get("267").get(temp.getVehiclePlateColorCode()));
            }
            if(!StringUtils.isEmpty(temp.getVehicleEnergyType()) && dictMap.containsKey("223")){
                //车辆能源类型
                temp.setVehicleEnergyType(dictMap.get("223").get(temp.getVehicleEnergyType()));
            }
            if(!StringUtils.isEmpty(temp.getVehicleType()) && dictMap.containsKey("20200304110857100003")){
                //车辆类型
                temp.setVehicleType(dictMap.get("20200304110857100003").get(temp.getVehicleType()));
            }
        }

        return list;
    }

    @Override
    public List<Vehicle> queryReportVehicle(Integer start, Integer end, Map<String,Object> paramMap) {
        List<String> statusList = (List<String>)paramMap.get("status");
        return vehicleBusinessRepository.queryReportVehicle(start,end,statusList);
    }


    private String getStatus(ReportMsgVO reportMsg, Vehicle vehicle){
//        if(vehicle.getReportNum().intValue()<=vehicle.getFinNum().intValue()){
//            //上报完成
//            return UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode();
//        }else{
            //多渠道只要有一个上报失败，状态为上报失败
            if(vehicle.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode()) ||
                reportMsg.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode())){
                return UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode();
            }

            //多渠道时，只要有没有返回结果的，都为上报中状态
            return UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode();
//        }
    }

    /**
     * 上报监管平台
     * @return
     * @throws JAXBException
     */
    @Transactional(rollbackFor = {Exception.class})
    public ResultMode report(Vehicle vehicle){
        vehicle.setBusiType(RabbitConstants.BusiType.WLHY_VEHICLE);
        vehicle.setBusiId(String.valueOf(vehicle.getId()));

        //设置网络货运主体
        ResultMode resultMode = busiMsgService.setNetworkId(vehicle);
        if(!resultMode.isSucceed()){
            return resultMode;
        }

        vehicle.setStatus(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        vehicle.setModifyDate(new Date());
        vehicle.setFinNum(0);
        String userBaseId = StrUtil.blankToDefault(JwtUtil.getInstance().getUserBaseIdByToken(), "");
        vehicle.setModifyBy(userBaseId);
        vehicle.setReporterId(userBaseId);
        vehicleBusinessRepository.updateReportingStatus(vehicle);

        resultMode =  busiMsgService.convertAndSend(vehicle);
        return resultMode;
    }

    /**
     * 转换重量，千克转换为吨
     * @param vehicle
     * @return
     */
    private void formatWeight(Vehicle vehicle){
        if(!StringUtils.isEmpty(vehicle.getVehicleTonnage())){
            //核定载质量由千克转换为吨
            vehicle.setVehicleTonnage(
                    new BigDecimal(vehicle.getVehicleTonnage())
                            .divide(new BigDecimal("1000"))
                            .setScale(2,BigDecimal.ROUND_HALF_UP).toString());
        }
        if(!StringUtils.isEmpty(vehicle.getGrossMass())){
            //吨位由千克转换为吨
            vehicle.setGrossMass(
                    new BigDecimal(vehicle.getGrossMass())
                            .divide(new BigDecimal("1000"))
                            .setScale(2,BigDecimal.ROUND_HALF_UP).toString());
        }
    }

    /**
     * 根据牵引车上报挂车
     *
     * @param vehicle
     * @return com.isoftstone.hig.common.model.ResultMode
     * <AUTHOR>
     * @Date 2023/7/17 17:10
     */
    /*private ResultMode reportTrailerByTractor(Vehicle vehicle) {
        ResultMode<String> resultModel = new ResultMode<>();

        String trailerNumber = vehicle.getTrailerVehiclePlateNumber();
        if (StrUtil.isNotBlank(trailerNumber)) {
            Vehicle trailerTemp = new Vehicle();
            trailerTemp.setVehicleNumber(trailerNumber);
            trailerTemp.setCarType(SuperviseEnmus.CarTypeEnum.CAR_TYPE_TRAILER.getCode());
            Vehicle trailer = vehicleBusinessRepository.queryVehicleBusInfo(trailerTemp);
            if (ObjectUtil.isNotEmpty(trailer)) {
                if (StrUtil.isNotBlank(vehicle.getNetworkMainBodyId())) {
                    trailer.setNetworkMainBodyId(vehicle.getNetworkMainBodyId());
                }
                if (IterUtil.isNotEmpty(vehicle.getNetworkMainBodyIdList())) {
                    trailer.setNetworkMainBodyIdList(vehicle.getNetworkMainBodyIdList());
                }
                //上报校验
                resultModel = checkVehicleReport(trailer);
                if (!resultModel.isSucceed()) {
                    return resultModel;
                }
                // 批量上报，设置默认上报网络主体时，report中会过滤出允许上报挂车的渠道
                resultModel = SpvSpringFactory.getAopProxy(this).report(trailer);
            } else {
                log.error("VehicleServiceImpl#reportTrailerByTractor，牵引车[{}],自动上报挂车信息[{}]没有找到", vehicle.getVehicleNumber(), trailerNumber);
                return ResultMode.fail("上报挂车信息[{" + trailerNumber + "}]没有找到");
            }
        }

        return resultModel;
    }*/

    /**
     * 转换枚举值
     * @param vehicle
     * @return
     */
    /*private void formatEnum(Vehicle vehicle){
        if(!StringUtils.isEmpty(vehicle.getVehiclePlateColorCode())){
            //转换车牌颜色代码
            vehicle.setVehiclePlateColorCode(getExternalEnumKey(UtilityEnum.SuperviseEnumMappingEnum.LICENSE_PLATE_COLOR.getCode(),vehicle.getVehiclePlateColorCode()));
        }
        if(!StringUtils.isEmpty(vehicle.getVehicleEnergyType())){
            //车辆能源类型
            vehicle.setVehicleEnergyType(getExternalEnumKey(UtilityEnum.SuperviseEnumMappingEnum.VEHICLE_ENERGY.getCode(),vehicle.getVehicleEnergyType()));
        }
        if(!StringUtils.isEmpty(vehicle.getVehicleType())){
            //车辆类型
            vehicle.setVehicleType(getExternalEnumKey(UtilityEnum.SuperviseEnumMappingEnum.OPERATING_VEHICLE_TYPE.getCode(),vehicle.getVehicleType()));
        }
    }*/

    //20200721192210100001
    /**
     * 查询使用性质名称
     * @param code
     * @return
     */
    /*private String getUseCharacterByKey(String code){
        PagingInfo<PlatformCmDictionaryFilter> pageInfo = new PagingInfo<>();
        PlatformCmDictionaryFilter filter = new PlatformCmDictionaryFilter();
        filter.setEnumCode(code);
        filter.setParentdicId("20200721192210100001");
        pageInfo.setFilterModel(filter);
        ResultMode<PlatformCmDictionary> dictionaryResultMode = platformCmDictionaryInter.platformCmDictionaryAllPaging(pageInfo);
        if(dictionaryResultMode != null && dictionaryResultMode.getModel().size() > 0){
            PlatformCmDictionary dictionary = dictionaryResultMode.getModel().get(0);
            return dictionary.getName();
        }
        return "";
    }*/

    /**
     * 转换枚举值--查询映射对应的名称
     * @param vehicle
     * @return
     */
    /*private void formatEnumName(Vehicle vehicle){
        if(!StringUtils.isEmpty(vehicle.getVehiclePlateColorCode())){
            //转换车牌颜色代码
            vehicle.setVehiclePlateColorCode(getExternalEnumNameByKey(UtilityEnum.SuperviseEnumMappingEnum.LICENSE_PLATE_COLOR.getCode(),vehicle.getVehiclePlateColorCode()));
        }
        if(!StringUtils.isEmpty(vehicle.getVehicleEnergyType())){
            //车辆能源类型
            vehicle.setVehicleEnergyType(getExternalEnumNameByKey(UtilityEnum.SuperviseEnumMappingEnum.VEHICLE_ENERGY.getCode(),vehicle.getVehicleEnergyType()));
        }
        if(!StringUtils.isEmpty(vehicle.getVehicleType())){
            //车辆类型
            vehicle.setVehicleType(getExternalEnumNameByKey(UtilityEnum.SuperviseEnumMappingEnum.OPERATING_VEHICLE_TYPE.getCode(),vehicle.getVehicleType()));
        }
        log.info("进入 VehicleServiceImpl.formatEnumName  方法中 查询匹配的 枚举值名称结果为{}", JSON.toJSONString(vehicle));
    }*/

    /**
     * 根据系统枚举key转换为货运平台key
     * @param enumType
     * @param systemEnumKey
     * @return
     */
    /*private String getExternalEnumKey(String enumType,String systemEnumKey){
        EnumMapping mapping = new EnumMapping();
        mapping.setEnumType(enumType);
        mapping.setSystemEnumKey(systemEnumKey);
        mapping = enumMappingRepository.queryEnumMappingById(mapping);
        if(mapping != null){
            return mapping.getExternalEnumKey();
        }
        return systemEnumKey;
    }*/


    /**
     * 查询映射对应的名称
     * @param enumType
     * @param externalEnumKey
     * @return
     */
  /*  private String getExternalEnumNameByKey(String enumType,String externalEnumKey){
        EnumMapping mapping = new EnumMapping();
        mapping.setEnumType(enumType);
        mapping.setExternalEnumKey(externalEnumKey);
        mapping = enumMappingRepository.queryEnumMappingById(mapping);
        if(mapping != null){
            return mapping.getExternalEnumValue() + "/" + externalEnumKey;
        }
        return null;
    }*/

    /**
     * searchExistsData:        检验是否重复操作
     * @param vo
     * @return
     */
    private List<Vehicle> searchExistsData(VehicleBusSearchVo vo){
        log.info("进入 DriverServiceImpl.searchExistsData  方法中查询参数{}", JSON.toJSONString(vo));
        log.info("检查开始。。。。。");
        List<Vehicle> result = vehicleBusinessRepository.searchExistsData(vo);
        if(result != null && result.size() >0){
            return result;
        }
        return null;
    }

    /**
     * 车辆信息重新上报-保存并上报
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultMode reportAgain(Vehicle vehicle) {
        //参数校验
        ResultMode resultMode = reportAgainCheck(vehicle);
        if (!resultMode.isSucceed()) {
            return resultMode;
        }

        //保存车辆信息
        saveVehicleBusInfo(vehicle);

        Vehicle temp = vehicleBusinessRepository.queryVehicleBusInfoById(vehicle.getId());
        if (temp == null) {
            throw new SupeWlydException("上报车辆信息[" + vehicle.getVehicleNumber() + "]没有找到");
        }
        temp.setNetworkMainBodyIdList(vehicle.getNetworkMainBodyIdList());
        //上报
        resultMode = SpvSpringFactory.getAopProxy(this).report(temp);

        return resultMode;
    }
    @Override
    public void autoReportVehicle(List<String> custIds, Integer pageSize) {
        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        custIds.forEach(custId ->{
            List<Vehicle> vehicleList = vehicleBusinessRepository.autoQueryReportVehicle(statusList, pageSize, custId);
            vehicleList = vehicleList.stream().filter(item -> {
                item.setNetworkMainBodyId(custId);
                return true;
            }).collect(Collectors.toList());
            reportVehicleList(vehicleList);
        });
    }

    @Override
    public void reportVehicleList(List<Vehicle> vehicleList){
        if (IterUtil.isEmpty(vehicleList)){
            return;
        }
        vehicleList.forEach(item ->{
            // 挂车-校验是否需要上报
            ResultMode<String> resultModel = checkReportTrailerChannel(item);
            if (!resultModel.isSucceed()) {
                log.info("自动车辆上报失败-校验是否需要上报，参数：{},原因：{}", JSON.toJSONString(item), resultModel.getErrMsg());
                return;
            }
            resultModel = SpvSpringFactory.getAopProxy(this).report(item);
            if (!resultModel.isSucceed()) {
                log.info("自动车辆上报失败，参数：{},原因：{}", JSON.toJSONString(item), resultModel.getErrMsg());
                return;
            }
        });
    }

    @Override
    public List<Vehicle> queryVehicle(Vehicle vehicle) {
        return vehicleBusinessRepository.queryVehicle(vehicle);
    }

    @Override
    public Vehicle queryByNumberColor(Vehicle vehicle) {
        return vehicleBusinessRepository.queryByNumberColor(vehicle.getVehicleNumber(), vehicle.getVehiclePlateColorCode());
    }

    @Override
    public void updateVehicleActualCarrier(String vehicleNumber, String actualCarrierName, String actualCarrierId) {
        vehicleBusinessRepository.updateVehicleActualCarrier(vehicleNumber, actualCarrierName, actualCarrierId);
    }

    private ResultMode reportAgainCheck(Vehicle vehicle) {
        if (null == vehicle) {
            return ResultMode.fail("车辆信息不允许为空");
        }
        if (StrUtil.isBlank(vehicle.getVehicleNumber())) {
            return ResultMode.fail("车牌号不允许为空");
        }

        if (IterUtil.isEmpty(vehicle.getNetworkMainBodyIdList())) {
            return ResultMode.fail("网络货运主体Id不允许为空");
        }

        for (String mainId : vehicle.getNetworkMainBodyIdList()) {
            //上报成功 才允许重新上报
            int count = messageNoteService.checkIsUploadData(String.valueOf(vehicle.getId()), RabbitConstants.BusiType.WLHY_VEHICLE, mainId);
            if (count == 0) {
                return ResultMode.fail("车辆[" + vehicle.getVehicleNumber() + "]未上报成功，不支持重新上报");
            }
        }

        //挂车校验上报渠道
        ResultMode model = checkReportTrailerChannel(vehicle);
        if (!model.isSucceed()) {
            return model;
        }

        return ResultMode.success();
    }

}
