package com.isoftstone.hig.supervise.service.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.api.entity.MessageNote;
import com.isoftstone.hig.supervise.api.entity.ReportMsgVO;
import com.isoftstone.hig.supervise.api.service.BusiMsgService;
import com.isoftstone.hig.supervise.service.application.model.command.ContractCommand;
import com.isoftstone.hig.supervise.service.application.model.command.ContractInfoReportCommand;
import com.isoftstone.hig.supervise.service.application.model.command.ContractReportCommand;
import com.isoftstone.hig.supervise.service.application.model.command.ViewReportCommand;
import com.isoftstone.hig.supervise.service.application.model.dto.ContractDetailDTO;
import com.isoftstone.hig.supervise.service.application.model.dto.ContractListDTO;
import com.isoftstone.hig.supervise.service.application.model.dto.ContractViewDTO;
import com.isoftstone.hig.supervise.service.application.model.query.ContractListQuery;
import com.isoftstone.hig.supervise.service.application.model.query.ViewReportInfoQuery;
import com.isoftstone.hig.supervise.service.domain.model.condition.ContractCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvContractInfoEntity;
import com.isoftstone.hig.supervise.service.domain.repository.ContractRepository;
import com.isoftstone.hig.supervise.service.enmus.SuperviseEnmus;
import com.isoftstone.hig.supervise.service.infrastructure.exception.SpvErrorCode;
import com.isoftstone.hig.supervise.service.rabbitmq.RabbitConstants;
import com.isoftstone.hig.supervise.service.repository.MessageNoteRepository;
import com.isoftstone.hig.supervise.service.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ContractAppService {
    @Resource
    private ContractRepository contractRepository;
    @Resource
    private BusiMsgService busiMsgService;
    @Resource
    private MessageNoteRepository messageNoteRepository;

    /**
     * 分页查询
     */
    public ResultMode<ContractListDTO> queryPage(PagingInfo<ContractListQuery> query) {
        Page<SpvContractInfoEntity> page = PageHelper.startPage(query.getCurrentPage(), query.getPageLength(), true);
        ContractCondition condition = BeanUtil.copyProperties(query.getFilterModel(), ContractCondition.class);
        List<SpvContractInfoEntity> list = contractRepository.queryList(condition);
        return ResultMode.successPageList(BeanUtil.copyToList(list, ContractListDTO.class), (int) page.getTotal());
    }

    /**
     * 删除
     */
    public void delete(Long id) {
        contractRepository.delete(id);
    }

    /**
     * 查看详情
     */
    public ContractDetailDTO getDetail(Long id) {
        return BeanUtil.toBean(contractRepository.getDetail(id), ContractDetailDTO.class);
    }

    /**
     * 保存
     */
    public void save(ContractCommand command) {
        SpvContractInfoEntity entity = BeanUtil.toBean(command, SpvContractInfoEntity.class);
        entity.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        contractRepository.update(entity);
    }

    /**
     * 提交
     */
    public ResultMode submit(ContractReportCommand command) {
        SpvContractInfoEntity accountInfoEntity = contractRepository.getDetail(command.getId());
        if (ObjUtil.isNull(accountInfoEntity)) {
            return ResultMode.fail(SpvErrorCode.BU_SPV_001.getMsg(), SpvErrorCode.BU_SPV_001.getCode());
        }
        if (StrUtil.equalsAny(accountInfoEntity.getStatus(), SuperviseEnmus.StatusEnum.DELETE.getCode())) {
            return ResultMode.fail(SpvErrorCode.BU_SPV_002.getMsg(), SpvErrorCode.BU_SPV_002.getCode());
        }

        List<MessageNote> messageNoteList = messageNoteRepository.queryReportingMessage(accountInfoEntity.getContractNo(), RabbitConstants.BusiType.WLHY_CONTRACT,
            command.getNetworkMainBodyIdList());
        if (CollUtil.isNotEmpty(messageNoteList)) {
            StringBuilder sb = new StringBuilder();
            List<String> reportingNetworkMainBodyNames = messageNoteList.stream().filter(messageNote -> "2".equals(messageNote.getMessageStatus())).map(MessageNote::getNetworkMainBodyName).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(reportingNetworkMainBodyNames)) {
                sb.append("网络货运主体[" + String.join(",", reportingNetworkMainBodyNames) + "]正在上报中.");
            }
            List<String> reportedNetworkMainBodyNames = messageNoteList.stream().filter(messageNote -> "1".equals(messageNote.getMessageStatus())).map(MessageNote::getNetworkMainBodyName).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(reportedNetworkMainBodyNames)) {
                sb.append("网络货运主体[" + String.join(",", reportedNetworkMainBodyNames) + "]已上报，请勿重复上报.");
            }
            if (sb.length() > 0) {
                return ResultMode.fail(sb.toString());
            }
        }
        //1、修改数据
        SpvContractInfoEntity entity = BeanUtil.toBean(command, SpvContractInfoEntity.class);
        entity.setStatus(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        contractRepository.update(entity);
        // 2、上报
        ViewReportCommand viewReportCommand = new ViewReportCommand(command.getId(), command.getNetworkMainBodyIdList());
        ContractInfoReportCommand reportCommand = getContractInfoReportCommand(viewReportCommand);
        //设置网络货运主体
        ResultMode resultMode = busiMsgService.setNetworkId(reportCommand);
        if (!resultMode.isSucceed()) {
            return resultMode;
        }

        return busiMsgService.convertAndSend(reportCommand);
    }


    private ContractInfoReportCommand getContractInfoReportCommand(ViewReportCommand viewReportCommand) {
        SpvContractInfoEntity entity = contractRepository.getDetail(viewReportCommand.getId());
        ContractInfoReportCommand reportCommand = BeanUtil.toBean(entity, ContractInfoReportCommand.class);
        reportCommand.setBusiType(RabbitConstants.BusiType.WLHY_CONTRACT);
        reportCommand.setBusiId(entity.getContractNo());
        reportCommand.setNetworkMainBodyIdList(viewReportCommand.getNetworkMainBodyIdList());
        reportCommand.setNetworkMainBodyId(viewReportCommand.getNetworkMainBodyId());
        return reportCommand;
    }

    /**
     * 批量上报
     */
    public ResultMode batchReport(List<Long> idList) {
        idList.forEach(id -> {
            ContractInfoReportCommand reportCommand = getContractInfoReportCommand(new ViewReportCommand(id));
            busiMsgService.setNetworkId(reportCommand);
            busiMsgService.convertAndSend(reportCommand);
        });
        return ResultMode.success();
    }

    /**
     * 查看映射结果
     */
    public ResultMode<ContractViewDTO> viewReportInfo(ViewReportInfoQuery query) {
        ViewReportCommand viewReportCommand = new ViewReportCommand(query.getId(), query.getNetworkMainBodyId());
        ContractInfoReportCommand reportCommand = getContractInfoReportCommand(viewReportCommand);

        ResultMode resultMode = busiMsgService.queryMappingResults(reportCommand);
        if (!resultMode.isSucceed()) {
            return ResultMode.success();
        }
        ContractViewDTO viewDTO = (ContractViewDTO) resultMode.getModel().get(0);
        return ResultMode.success(viewDTO);
    }

    /**
     * 上报回调
     */
    public void updateReportStatus(ReportMsgVO reportMsgVO) {
        if (StringUtils.isEmpty(reportMsgVO.getBusiId())) {
            return;
        }

        SpvContractInfoEntity contract = contractRepository.getByContractNo(reportMsgVO.getBusiId());
        if (ObjUtil.isNull(contract)) {
            log.info("updateReportStatus#合同信息不存在:{}", reportMsgVO.getBusiId());
            return;
        }

        SpvContractInfoEntity contractUpd = new SpvContractInfoEntity();
        contractUpd.setId(contract.getId());
        contractUpd.setSendToProDateTime(reportMsgVO.getReportTime());
        contractUpd.setStatus(reportMsgVO.getStatus());
        contractUpd.setSuccessMessage(reportMsgVO.getResultInfo());
        contractUpd.setSuccessMessage(StringUtils.substring(contractUpd.getSuccessMessage(), 0, 255));
        contractRepository.update(contractUpd);
    }

}
