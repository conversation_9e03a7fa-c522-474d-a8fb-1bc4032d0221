package com.isoftstone.hig.supervise.service.application.service;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.supervise.service.application.model.dto.CarrierDetailDTO;
import com.isoftstone.hig.supervise.service.application.model.dto.ContractListDTO;
import com.isoftstone.hig.supervise.service.application.model.query.ContractListQuery;
import com.isoftstone.hig.supervise.service.domain.model.condition.ContractCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvContractBusInfoEntity;
import com.isoftstone.hig.supervise.service.domain.service.ContractDomainService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ContractAppService {
    @Resource
    private ContractDomainService contractDomainService;

    /**
     * 分页查询
     */
    public ResultMode<ContractListDTO> queryPage(PagingInfo<ContractListQuery> query) {
        Page<SpvContractBusInfoEntity> page = PageHelper.startPage(query.getCurrentPage(), query.getPageLength(), true);
        ContractCondition condition = BeanUtil.copyProperties(query.getFilterModel(), ContractCondition.class);
        List<SpvContractBusInfoEntity> list = contractDomainService.queryList(condition);
        return ResultMode.successPageList(BeanUtil.copyToList(list, ContractListDTO.class), (int) page.getTotal());
    }

    /**
     * 删除
     */
    public void delete(Long id) {
        contractDomainService.delete(id);
    }

    /**
     * 查看详情
     */
    public CarrierDetailDTO getDetail(Long id) {
        return BeanUtil.toBean(contractDomainService.getDetail(id), CarrierDetailDTO.class);
    }
}
