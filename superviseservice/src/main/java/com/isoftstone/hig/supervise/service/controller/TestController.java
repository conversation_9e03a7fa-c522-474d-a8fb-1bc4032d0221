package com.isoftstone.hig.supervise.service.controller;

import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.KafkaUtil;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.supervise.api.entity.Waybill;
import com.isoftstone.hig.supervise.service.application.service.WaybillAppService;
import com.isoftstone.hig.supervise.service.enmus.KaFkaTopicNameEnmu;
import com.isoftstone.hig.supervise.service.pojo.BmsTerminationPay;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Log4j2
@RestController
public class TestController {

    @Autowired
    private KafkaUtil kafkaUtil;

    @Resource
    private WaybillAppService waybillService;


    @PostMapping("/testTerminationWaybillPayment")
    public ResultMode<String> testTerminationWaybillPayment(@RequestBody Waybill w) {
        waybillService.updateTerminationWaybillAmountByWaybillId(w.getSerialNumber(), w.getTransportCosts());
        return ResultMode.success();
    }

    @PostMapping("/testKafkaTerminationWaybillPayment")
    public ResultMode<String> testKafkaTerminationWaybillPayment(@RequestBody Waybill w) {
        BmsTerminationPay bmsTerminationPay = new BmsTerminationPay();
        bmsTerminationPay.setBusBillId(w.getSerialNumber());
        bmsTerminationPay.setActualAmount(w.getTransportCosts());

        String topic = SpringContextUtil.getKafkaTopicPrefix() + KaFkaTopicNameEnmu.TERMINATION_PAY.getTopicName();
        String sendData = "{\"actualAmount\":731.20,\"busBillId\":\"YD2023032800000008\"}";
        //JSONObject.toJSONString(bmsTerminationPay);
        log.info("##########################################");
        log.info("##########################################");
        log.info("kafka主题:{}", topic);
        log.info("kafka发送数据:{}", sendData);
        log.info("##########################################");
        log.info("##########################################");
        kafkaUtil.kafkaProducerSend(topic, sendData);
        return ResultMode.success();
    }
}
