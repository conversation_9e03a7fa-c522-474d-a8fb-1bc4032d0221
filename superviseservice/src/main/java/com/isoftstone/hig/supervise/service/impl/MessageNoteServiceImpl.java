package com.isoftstone.hig.supervise.service.impl;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.RedisUtil;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.api.entity.*;
import com.isoftstone.hig.supervise.api.service.*;
import com.isoftstone.hig.supervise.service.application.service.CarrierAppService;
import com.isoftstone.hig.supervise.service.application.service.ContractAppService;
import com.isoftstone.hig.supervise.service.application.service.ShipperAppService;
import com.isoftstone.hig.supervise.service.application.service.WaybillAppService;
import com.isoftstone.hig.supervise.service.rabbitmq.RabbitConstants;
import com.isoftstone.hig.supervise.service.repository.MessageNoteRepository;
import com.isoftstone.hig.supervise.service.utils.StringUtils;
import com.isoftstone.hig.supervise.service.utils.SuperviseConstants;
import com.isoftstone.hig.supervise.service.utils.exception.SupeWlydException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Service
public class MessageNoteServiceImpl implements MessageNoteService {

    @Autowired
    private DriverService driverService;

    @Autowired
    private VehicleService vehicleService;

    @Autowired
    private WaybillAppService waybillService;

    @Autowired
    private CapitalAccountService capitalAccountService;

    @Resource
    MessageNoteRepository messageNoteRepository;

    @Resource
    private BusiMsgService busiMsgService;

    @Autowired
    private ChannelConfigService channelConfigService;

    @Resource
    private ShipperAppService shipperAppService;

    @Resource
    private CarrierAppService carrierAppService;

    @Resource
    private ContractAppService contractAppService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleReportResults(ReportMsgVO reportMsgVO) {
        if(StringUtils.isEmpty(reportMsgVO.getBusiType())){
            return;
        }

        reportMsgVO.setResultInfo(StringUtils.substring(reportMsgVO.getResultInfo(),0,255));
        switch (reportMsgVO.getBusiType()) {
            case RabbitConstants.BusiType.WLHY_DRIVER:
                driverService.updateReportStatus(reportMsgVO);
                break;
            case RabbitConstants.BusiType.WLHY_VEHICLE:
                vehicleService.updateReportStatus(reportMsgVO);
                break;
            case RabbitConstants.BusiType.WLHY_WAYBILL:
                waybillService.updateReportStatus(reportMsgVO);
                break;
            case RabbitConstants.BusiType.WLHY_ACCOUNT:
                capitalAccountService.updateReportStatus(reportMsgVO);
                break;
            case RabbitConstants.BusiType.WLHY_SHIPPER:
                shipperAppService.updateReportStatus(reportMsgVO);
                break;
            case RabbitConstants.BusiType.WLHY_ACTUAL_CARRIER:
                carrierAppService.updateReportStatus(reportMsgVO);
                break;
            case RabbitConstants.BusiType.WLHY_CONTRACT:
                contractAppService.updateReportStatus(reportMsgVO);
                break;
            default:
                return;
        }

        //上报失败存到Redis
        handleErrorResults(reportMsgVO);

        MessageNote messageNote = toMessageNote(reportMsgVO);
        insertReportMsg(messageNote);
    }

    /**
     * 上报失败存到Redis
     */
    private void handleErrorResults(ReportMsgVO reportMsgVO) {
        String busiType = reportMsgVO.getBusiType();
        if (!StrUtil.equalsAny(busiType, RabbitConstants.BusiType.WLHY_WAYBILL, RabbitConstants.BusiType.WLHY_ACCOUNT)) {
            return;
        }
        //判断是否上报状态
        if (!StrUtil.equalsAny(reportMsgVO.getStatus(), UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode(),
            UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode())) {
            return;
        }
        //判断网络货运主体，是否开启自动上报
        List<String> autoReportCustIds = channelConfigService.getAutoReportCustIds();
        if (IterUtil.isEmpty(autoReportCustIds) || !autoReportCustIds.contains(reportMsgVO.getNetworkMainBodyId())) {
            return;
        }
        //键值
        StringBuffer keyBuffer = new StringBuffer();
        if (StrUtil.equals(busiType, RabbitConstants.BusiType.WLHY_WAYBILL)) {
            keyBuffer.append(SuperviseConstants.SUPERVISE_WAYBILL_REPORT_ERROR_REDIS_KEY);
        } else if (StrUtil.equals(busiType, RabbitConstants.BusiType.WLHY_ACCOUNT)) {
            keyBuffer.append(SuperviseConstants.SUPERVISE_ACCOUNT_REPORT_ERROR_REDIS_KEY);
        }
        String key = keyBuffer.append(reportMsgVO.getBusiId()).toString();

        //判断redis中是否已存在
        if (RedisUtil.exists(key)) {
            if (StrUtil.equals(reportMsgVO.getStatus(), UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode())) {
                //上报成功后删除
                RedisUtil.del(key);
                log.info("handleErrorResults#上报成功删除Redis：{}", key);
            }
            return;
        }

        //保存到redis
        RedisUtil.set(key, JSONUtil.toJsonStr(reportMsgVO), 60 * 90);

    }

    private MessageNote toMessageNote(ReportMsgVO reportMsgVO){
        Date curDate = new Date();
        MessageNote messageNote = new MessageNote();
        messageNote.setMessageType(reportMsgVO.getBusiType());
        messageNote.setMessageContentId(reportMsgVO.getBusiId());
        messageNote.setMessageTime(reportMsgVO.getReportTime());
        messageNote.setMessageStatus(reportMsgVO.getStatus());
        messageNote.setReportMessage(reportMsgVO.getReportMessage());
        messageNote.setResultMessage(reportMsgVO.getResultMessage());
        messageNote.setCreateDate(curDate);
        messageNote.setModifyDate(curDate);
        messageNote.setNetworkMainBodyId(reportMsgVO.getNetworkMainBodyId());
        messageNote.setNetworkMainBodyName(reportMsgVO.getNetworkMainBodyName());
        messageNote.setResultInfo(reportMsgVO.getResultInfo());
        return messageNote;
    }

    @Override
    public int insertReportMsg(MessageNote messageNote) {
        if(messageNote.getMessageStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode())){
            messageNote.setMessageStatus("1");
        }else if(messageNote.getMessageStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode())){
            messageNote.setMessageStatus("0");
        }

        //删除记录
        messageNoteRepository.deleteMessageNoteByMessageId(messageNote.getMessageContentId(),messageNote.getMessageType(),messageNote.getNetworkMainBodyId());

        messageNote.setMessageId(UtilityClass.uuid());
        messageNote.setResultInfo(StringUtils.substring(messageNote.getResultInfo(),0,255));
        return messageNoteRepository.insertMessageNote(messageNote);
    }

    @Override
    public ResultMode<MessageNote> queryMessagePageInfo(PagingInfo<MessageNote> pageInfo) {
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        List<MessageNote> list = messageNoteRepository.queryMessagePageInfo(pageInfo.filterModel);
        PageInfo<MessageNote> returnPageInfo = new PageInfo<>(list);
        ResultMode<MessageNote> mode = new ResultMode<>();
        mode.setTotal((int) returnPageInfo.getTotal());
        mode.setModel(returnPageInfo.getList());
        return mode;
    }

    @Override
    public int checkIsUploadData(String messageContentId,String messageType,String networkMainBodyId) {
        if(StringUtils.isEmpty(messageContentId) || StringUtils.isEmpty(messageType) || StringUtils.isEmpty(networkMainBodyId)){
            return 0;
        }
        MessageNote messageNote = new MessageNote();
        messageNote.setMessageContentId(messageContentId);
        messageNote.setMessageType(messageType);
        messageNote.setNetworkMainBodyId(networkMainBodyId);
        return messageNoteRepository.checkIsUploadData(messageNote);
    }

    @Override
    public List<String> checkIsUploadDatas(List<String> messageContentIdList, String messageType, String networkMainBodyId) {
        if (messageContentIdList == null) {
            throw new SupeWlydException("批量校验数据是否经上传，参数错误");
        }

        List<String> unUploadDataList = new ArrayList<>();
        messageContentIdList.forEach(messageContentId -> {
            int count = checkIsUploadData(messageContentId, messageType, networkMainBodyId);
            if (count == 0) {
                unUploadDataList.add(messageContentId);
            }
        });

        return unUploadDataList;
    }

    @Override
    public int checkIsDriverReport(String drivingLicense, String networkMainBodyId) {
        if(StringUtils.isEmpty(drivingLicense) || StringUtils.isEmpty(networkMainBodyId)){
            return 0;
        }
        return messageNoteRepository.checkIsDriverReport(drivingLicense,networkMainBodyId,null);
    }

    @Override
    public int checkIsDriverReportWitchDelay(String drivingLicense, String networkMainBodyId,Integer delayMinute) {
        if(StringUtils.isEmpty(drivingLicense) || StringUtils.isEmpty(networkMainBodyId) || ObjUtil.isNull(delayMinute)){
            return 0;
        }
        return messageNoteRepository.checkIsDriverReport(drivingLicense,networkMainBodyId,delayMinute);
    }

    @Override
    public List<MessageNote> queryMessageNoteByBusiId(String messageContentId, String messageType, String networkMainBodyId) {
        if(StringUtils.isEmpty(messageContentId)){
            return null;
        }
        return messageNoteRepository.queryMessageNoteByBusiId(messageContentId, messageType, networkMainBodyId);
    }

    @Override
    public ResultMode<MessageNote> queryReportMessage(MessageNote messageNote) {
        if(StringUtils.isEmpty(messageNote.getMessageType()) || StringUtils.isEmpty(messageNote.getNetworkMainBodyId()) ||
            StringUtils.isEmpty(messageNote.getMessageContentId())){
            return ResultMode.success();
        }
        List<com.wanlianyida.dcs.entity.Message> messagesList = busiMsgService.queryDcsMessage(messageNote.getMessageType(),messageNote.getNetworkMainBodyId()+messageNote.getMessageContentId());
        if (messageNote.getMessageType().equals(RabbitConstants.BusiType.WLHY_VEHICLE)) {
            if (StringUtils.isEmpty(messagesList)){
                messagesList = busiMsgService.queryDcsMessage(messageNote.getMessageType(), messageNote.getNetworkMainBodyId() + messageNote.getVehicleId());
            }
        }
        if(StringUtils.isEmpty(messagesList)){
            return ResultMode.success();
        }
        for(com.wanlianyida.dcs.entity.Message message:messagesList){
            message.setCreatDate(message.getCreatDate()==null?"":message.getCreatDate());
        }

        //按创建时间降序,取最新记录
        messagesList.sort(Comparator.comparing(com.wanlianyida.dcs.entity.Message::getCreatDate).reversed());

        com.wanlianyida.dcs.entity.Message message = messagesList.get(0);
        MessageNote temp = new MessageNote();
        temp.setReportMessage(message.getRequest());
        temp.setResultMessage(message.getResponse());
        return ResultMode.success(temp);
    }

    @Override
    public List<MessageNote> queryReturnMsgByBusiId(String messageContentId, String messageType, String networkMainBodyId) {
        if(StringUtils.isEmpty(messageContentId) || StringUtils.isEmpty(messageType)){
            return new ArrayList<>(0);
        }
        List<MessageNote> list =  messageNoteRepository.queryReturnMsgByBusiId(messageContentId,messageType,networkMainBodyId);
        if(StringUtils.isEmpty(list)){
            return new ArrayList<>(0);
        }
        for(MessageNote msg:list){
            if(msg==null){
                continue;
            }
            //网络货运主体企业简称(code：message)
            if(StringUtils.isEmpty(msg.getNetworkMainBodyName())){
                continue;
            }
            msg.setResultInfo(msg.getNetworkMainBodyName()+"("+msg.getResultInfo()+")");
        }
        return list;
    }

    @Override
    public ResultMode<ChannelVO> queryChannel(MessageNote channel) {
        List<SuperviseChannelConfig> configList =  channelConfigService.getConfigs();
        if(StringUtils.isEmpty(configList)){
            return ResultMode.success();
        }
        if (StrUtil.equals(channel.getMessageType(), RabbitConstants.BusiType.WLHY_VEHICLE)){
            ResultMode<Vehicle> resultMode = vehicleService.viewVehicleBusDetailById(channel.getMessageContentId());
            if (resultMode.isSucceed() && IterUtil.isNotEmpty(resultMode.getModel())
                && StrUtil.endWith(resultMode.getModel().get(0).getVehicleNumber(), "挂")) {
                //过滤挂车上报渠道
                configList = channelConfigService.getTrailerReportChannel(configList);
            }
        } else if (StrUtil.equalsAny(channel.getMessageType(),
            RabbitConstants.BusiType.WLHY_SHIPPER,
            RabbitConstants.BusiType.WLHY_ACTUAL_CARRIER,
            RabbitConstants.BusiType.WLHY_CONTRACT)) {
            //托运人、实际承运人、合同 过滤上报渠道
            configList = channelConfigService.getBusiTypeReportChannel(configList, channel.getMessageType());
        }

        List<ChannelVO> list = new ArrayList<>(8);
        for(SuperviseChannelConfig config:configList){
            ChannelVO temp = new ChannelVO();
            temp.setNetworkMainBodyId(config.getCustId());
            temp.setNetworkMainBodyName(config.getCustName());
            temp.setCheckDefault(config.getCheckDefault());
            temp.setPlatformCode(config.getPlatformCode());
            list.add(temp);
        }

        if(!StringUtils.isEmpty(channel.getMessageContentId()) && !StringUtils.isEmpty(channel.getMessageType())){
            Map<String,String> map = getNetworkMainBodyStatus(channel.getMessageContentId(), channel.getMessageType());
            for(ChannelVO temp:list){
                temp.setMessageStatus(map.get(temp.getNetworkMainBodyId()));
            }
        }
        return ResultMode.success(list);
    }

    @Override
    public int countSucReportNum(String messageContentId, String messageType) {
        if(StringUtils.isEmpty(messageContentId) || StringUtils.isEmpty(messageType)){
            return 0;
        }
        return messageNoteRepository.countSucReportNum(messageContentId,messageType);
    }

    @Override
    public Map<String, String> getNetworkMainBodyStatus(String messageContentId, String messageType) {
        List<MessageNote> list = messageNoteRepository.queryMessageNoteByBusiId(messageContentId,messageType,null);
        if(StringUtils.isEmpty(list)){
            return new HashMap<>(0);
        }
        Map<String,String> map = list.stream().filter(s->!StringUtils.isEmpty(s.getNetworkMainBodyId())).collect(Collectors.toMap(MessageNote::getNetworkMainBodyId,v->v.getMessageStatus(),(k1, k2) -> k1));
        return map;
    }

    @Override
    public ResultMode checkNetworkMainBodyStatus(String messageContentId, String messageType, List<String> networkIdList) {
        if(StringUtils.isEmpty(networkIdList)){
            return ResultMode.success();
        }
        List<MessageNote> list = messageNoteRepository.queryMessageNoteByBusiId(messageContentId,messageType,null);
        if(StringUtils.isEmpty(list)){
            return ResultMode.success();
        }
        Map<String,MessageNote> map = list.stream().filter(s->!StringUtils.isEmpty(s.getNetworkMainBodyId())).collect(Collectors.toMap(MessageNote::getNetworkMainBodyId,v->v,(k1, k2) -> k1));

        //判断是否有网络主体已在上报中或上报成功
        for(String id:networkIdList){
            MessageNote temp = map.get(id);
            if(temp==null){
                continue;
            }
            //0上报失败，1上报成功，2上报中
            if("1".equals(temp.getMessageStatus())){
                return ResultMode.fail("网络货运主体["+temp.getNetworkMainBodyName()+"]已上报");
            }else if("2".equals(temp.getMessageStatus())){
                return ResultMode.fail("网络货运主体["+temp.getNetworkMainBodyName()+"]正在上报中");
            }
        }
        return ResultMode.success();
    }

    @Override
    public ResultMode syncMessageToDcs() {
        List<MessageNote> list = messageNoteRepository.queryReportMessage();
        if(StringUtils.isEmpty(list)){
            return ResultMode.fail("查询数据为空");
        }
        //0成功 1失败
        String status="";
        for(MessageNote msg:list){
            if(StringUtils.isEmpty(msg.getMessageType()) || StringUtils.isEmpty(msg.getNetworkMainBodyId()) ||
                StringUtils.isEmpty(msg.getMessageContentId())){
                continue;
            }
            if(msg.getMessageStatus().equals("1")){
                status = RabbitConstants.DcsData.DCS_SUCCESS;
            }else{
                status = RabbitConstants.DcsData.DCS_ERROR;
            }
            //报文存入mongo db
            busiMsgService.sendDcsMessage(msg.getMessageType(),msg.getNetworkMainBodyId()+msg.getMessageContentId(),msg.getReportMessage(),msg.getResultMessage(),status);
        }
        log.info("=========同步完成=============");
        return ResultMode.success("同步完成");
    }

    @Override
    public int deleteMessageNoteByMessageContentId(String messageContentId,String messageType) {
        if(StringUtils.isEmpty(messageContentId)){
            return 0;
        }
        return messageNoteRepository.deleteMessageNoteByMessageContentId(messageContentId,messageType);
    }


}
