package com.isoftstone.hig.supervise.service.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.service.domain.model.condition.ContractCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvContractBusInfoEntity;
import com.isoftstone.hig.supervise.service.domain.repository.ContractRepository;
import com.isoftstone.hig.supervise.service.infrastructure.repository.mapper.SpvContractBusInfoMapper;
import com.isoftstone.hig.supervise.service.infrastructure.repository.po.SpvContractBusInfoPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ContractRepositoryImpl implements ContractRepository {
    @Resource
    private SpvContractBusInfoMapper superviseContractBusInfoMapper;

    @Override
    public List<SpvContractBusInfoEntity> queryList(ContractCondition condition) {
        LambdaQueryWrapper<SpvContractBusInfoPO> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.like(StrUtil.isNotBlank(condition.getEnterpriseName()), FinaCreditPO::getEnterpriseName, condition.getEnterpriseName());
//        queryWrapper.eq(StrUtil.isNotBlank(condition.getEnterpriseNameEqual()), FinaCreditPO::getEnterpriseName, condition.getEnterpriseNameEqual());
//        queryWrapper.eq(StrUtil.isNotBlank(condition.getModelRating()), FinaCreditPO::getModelRating, condition.getModelRating());
//        queryWrapper.in(CollUtil.isNotEmpty(condition.getEvaluateStatusList()), FinaCreditPO::getEvaluateStatus, condition.getEvaluateStatusList());
//        queryWrapper.orderByDesc(SuperviseCarrierBusInfoPO::getCreateTime);
        List<SpvContractBusInfoPO> list = superviseContractBusInfoMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(list, SpvContractBusInfoEntity.class);
    }

    @Override
    public void delete(Long id) {
        SpvContractBusInfoPO po = new SpvContractBusInfoPO();
        po.setId(id);
        //设置为删除状态
        po.setStatus(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        // TODO: 2025/6/27
//        po.setModifyBy(JwtUtil.getInstance().getUserBaseIdByToken());
//        po.setModifyDate(DateUtil.date());
        superviseContractBusInfoMapper.updateById(po);
    }

    @Override
    public SpvContractBusInfoEntity getDetail(Long id) {
        SpvContractBusInfoPO po = superviseContractBusInfoMapper.selectById(id);
        return BeanUtil.toBean(po, SpvContractBusInfoEntity.class);
    }
}
