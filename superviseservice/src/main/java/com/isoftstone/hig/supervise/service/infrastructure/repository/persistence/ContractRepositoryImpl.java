package com.isoftstone.hig.supervise.service.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.service.domain.model.condition.ContractCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvContractInfoEntity;
import com.isoftstone.hig.supervise.service.domain.repository.ContractRepository;
import com.isoftstone.hig.supervise.service.infrastructure.repository.mapper.SpvContractBusInfoMapper;
import com.isoftstone.hig.supervise.service.infrastructure.repository.po.SpvContractInfoPO;
import com.isoftstone.hig.supervise.service.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ContractRepositoryImpl implements ContractRepository {
    @Resource
    private SpvContractBusInfoMapper superviseContractBusInfoMapper;

    @Override
    public void init(SpvContractInfoEntity entity) {
        if(StrUtil.isEmpty(entity.getContractNo())){
            return;
        }

        SpvContractInfoPO po = BeanUtil.toBean(entity, SpvContractInfoPO.class);
        entity.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        po.setLastUpdateTime(DateUtil.date());

        List<SpvContractInfoEntity> list = queryList(new ContractCondition(entity.getContractNo()));
        if (CollUtil.isNotEmpty(list)) {
            po.setId(list.get(0).getId());
            superviseContractBusInfoMapper.updateById(po);
        } else {
            po.setId(IdUtil.generateId());
            po.setCreateTime(DateUtil.date());
            po.setStatus(UtilityEnum.SuperviseStatusEnum.TO_DO.getCode());
            superviseContractBusInfoMapper.insert(po);
        }
    }

    @Override
    public List<SpvContractInfoEntity> queryList(ContractCondition condition) {
        LambdaQueryWrapper<SpvContractInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(condition.getContractNo()), SpvContractInfoPO::getContractNo, condition.getContractNo());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getStatus()), SpvContractInfoPO::getStatus, condition.getStatus());
        queryWrapper.ge(ObjUtil.isNotNull(condition.getStartSendToProDateTime()), SpvContractInfoPO::getSendToProDateTime, condition.getStartSendToProDateTime());
        queryWrapper.le(ObjUtil.isNotNull(condition.getEndSendToProDateTime()), SpvContractInfoPO::getSendToProDateTime, condition.getEndSendToProDateTime());
        queryWrapper.eq(ObjUtil.isNotNull(condition.getContractType()), SpvContractInfoPO::getContractType, condition.getContractType());
        queryWrapper.ge(ObjUtil.isNotNull(condition.getStartContractSignTime()), SpvContractInfoPO::getContractSignTime, condition.getStartContractSignTime());
        queryWrapper.le(ObjUtil.isNotNull(condition.getEndContractSignTime()), SpvContractInfoPO::getContractSignTime, condition.getEndContractSignTime());
        queryWrapper.orderByDesc(SpvContractInfoPO::getCreateTime);
        List<SpvContractInfoPO> list = superviseContractBusInfoMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(list, SpvContractInfoEntity.class);
    }

    @Override
    public void delete(Long id) {
        SpvContractInfoPO po = new SpvContractInfoPO();
        po.setId(id);
        //设置为删除状态
        po.setStatus(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        po.setLastUpdaterId(JwtUtil.getInstance().getUserBaseIdByToken());
        po.setLastUpdateTime(DateUtil.date());
        superviseContractBusInfoMapper.updateById(po);
    }

    @Override
    public SpvContractInfoEntity getDetail(Long id) {
        SpvContractInfoPO po = superviseContractBusInfoMapper.selectById(id);
        return BeanUtil.toBean(po, SpvContractInfoEntity.class);
    }

    @Override
    public void update(SpvContractInfoEntity entity) {
        SpvContractInfoPO po = BeanUtil.toBean(entity, SpvContractInfoPO.class);
        po.setLastUpdaterId(JwtUtil.getInstance().getUserBaseIdByToken());
        po.setLastUpdateTime(DateUtil.date());
        superviseContractBusInfoMapper.updateById(po);
    }

    @Override
    public SpvContractInfoEntity getByContractNo(String contractNo) {
        if (StrUtil.isBlank(contractNo)) {
            return null;
        }

        LambdaQueryWrapper<SpvContractInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpvContractInfoPO::getContractNo, contractNo);
        return BeanUtil.toBean(superviseContractBusInfoMapper.selectOne(queryWrapper), SpvContractInfoEntity.class);
    }
}
