package com.isoftstone.hig.supervise.service.repository;

import com.isoftstone.hig.supervise.api.entity.MessageNote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MessageNoteRepository {


    /**
     * 保存报文信息
     *
     * @param messageNote 消息
     * @return int
     */
    int insertMessageNote(MessageNote messageNote);

    /**
     * 删除上报消息
     *
     * @param messageContentId  消息id
     * @param messageType       消息类型
     * @param networkMainBodyId 网络主体id
     * @return int
     */
    int deleteMessageNoteByMessageId(String messageContentId,String messageType,String networkMainBodyId);

    /**
     * 删除上报消息
     *
     * @param messageContentId 消息id
     * @param messageType       消息类型
     * @return int
     */
    int deleteMessageNoteByMessageContentId(String messageContentId,String messageType);

    /**
     * 报文分页列表
     * @param messageNote
     * @return
     */
    List<MessageNote> queryMessagePageInfo(MessageNote messageNote);

    /**
     * 校验数据是否已经上传
     * @param messageNote
     * @return
     */
    int checkIsUploadData(MessageNote messageNote);

    /**
     * 检查司机是否上报
     *
     * @param drivingLicense    身份证号
     * @param networkMainBodyId 网络主体id
     * @return int
     */
    int checkIsDriverReport(@Param("drivingLicense") String drivingLicense, @Param("networkMainBodyId")String networkMainBodyId,@Param("delayMinute")Integer delayMinute);


    /**
     * 查询上报信息
     *
     * @param messageContentId  业务id
     * @param messageType       业务类型
     * @param networkMainBodyId 网络主体id
     * @return {@link List}<{@link MessageNote}>
     */
    List<MessageNote> queryMessageNoteByBusiId(String messageContentId,String messageType,String networkMainBodyId);


    /**
     * 查询返回信息
     *
     * @param messageContentId  消息内容id
     * @param messageType       消息类型
     * @param networkMainBodyId 主体id
     * @return {@link List}<{@link String}>
     */
    List<MessageNote> queryReturnMsgByBusiId(String messageContentId, String messageType, String networkMainBodyId);


    /**
     * 统计上报成功数量
     *
     * @param messageContentId 消息内容id
     * @param messageType      消息类型
     * @return int
     */
    int countSucReportNum(String messageContentId,String messageType);

    /**
     * 查询所有上报的消息
     *
     * @return {@link List}<{@link MessageNote}>
     */
    List<MessageNote> queryReportMessage();


    /**
     * 检查上报中记录
     * @param messageContentId
     * @param messageType
     * @param networkMainBodyIdList
     * @return
     */
    List<MessageNote> queryReportingMessage(@Param("messageContentId") String messageContentId,@Param("messageType")String messageType,
                                               @Param("networkMainBodyIdList")List<String> networkMainBodyIdList);

}
