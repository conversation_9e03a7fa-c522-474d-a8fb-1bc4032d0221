package com.isoftstone.hig.supervise.service.controller;

import com.alibaba.fastjson.JSON;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.supervise.api.entity.ChannelVO;
import com.isoftstone.hig.supervise.api.entity.MessageNote;
import com.isoftstone.hig.supervise.api.inter.MessageNoteInter;
import com.isoftstone.hig.supervise.api.service.ConsumerMessageService;
import com.isoftstone.hig.supervise.api.service.MessageNoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@RestController
public class MessageNoteController implements MessageNoteInter {

    @Resource
    MessageNoteService messageNoteService;

    @Autowired
    private ConsumerMessageService consumerMessageService;

    @Override
    public ResultMode<MessageNote> queryMessagePageInfo(PagingInfo<MessageNote> pageInfo) {
        if (pageInfo == null) {
            ResultMode<MessageNote> resultModel = new ResultMode<>();
            resultModel.setErrMsg("pageInfo为空");
            resultModel.setSucceed(false);
            return resultModel;
        }

        ResultMode<MessageNote> mode = messageNoteService.queryMessagePageInfo(pageInfo);
        log.info("上报列表查询结果：{}", JSON.toJSONString(mode));
        return mode;
    }

    @Override
    public ResultMode<MessageNote> queryReportMessage(MessageNote messageNote) {
        return messageNoteService.queryReportMessage(messageNote);
    }

    @Override
    public ResultMode<ChannelVO> queryChannel(MessageNote channel) {
        return messageNoteService.queryChannel(channel);
    }

    @Override
    public ResultMode<ChannelVO> queryReportedChannel(MessageNote messageNote) {
        List<MessageNote> list = messageNoteService.queryMessageNoteByBusiId(messageNote.getMessageContentId(),messageNote.getMessageType(),messageNote.getNetworkMainBodyId());
        return ResultMode.success(list);
    }

    @Override
    public ResultMode queryReturnMsgByBusiId(MessageNote messageNote) {
        List<MessageNote> list =  messageNoteService.queryReturnMsgByBusiId(messageNote.getMessageContentId(),messageNote.getMessageType(), messageNote.getNetworkMainBodyId());
        return ResultMode.success(list);
    }

    @Override
    public ResultMode syncMessageToDcs() {
        return messageNoteService.syncMessageToDcs();
    }

}
