package com.isoftstone.hig.supervise.service.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.api.entity.MessageNote;
import com.isoftstone.hig.supervise.api.entity.ReportMsgVO;
import com.isoftstone.hig.supervise.api.service.BusiMsgService;
import com.isoftstone.hig.supervise.service.application.model.command.ShipperCommand;
import com.isoftstone.hig.supervise.service.application.model.command.ShipperInfoReportCommand;
import com.isoftstone.hig.supervise.service.application.model.command.ShipperReportCommand;
import com.isoftstone.hig.supervise.service.application.model.command.ViewReportCommand;
import com.isoftstone.hig.supervise.service.application.model.dto.ShipperDetailDTO;
import com.isoftstone.hig.supervise.service.application.model.dto.ShipperListDTO;
import com.isoftstone.hig.supervise.service.application.model.dto.ShipperViewDTO;
import com.isoftstone.hig.supervise.service.application.model.query.ShipperListQuery;
import com.isoftstone.hig.supervise.service.application.model.query.ViewReportInfoQuery;
import com.isoftstone.hig.supervise.service.domain.model.condition.ShipperCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvShipperInfoEntity;
import com.isoftstone.hig.supervise.service.domain.repository.ShipperRepository;
import com.isoftstone.hig.supervise.service.enmus.SuperviseEnmus;
import com.isoftstone.hig.supervise.service.infrastructure.exception.SpvErrorCode;
import com.isoftstone.hig.supervise.service.rabbitmq.RabbitConstants;
import com.isoftstone.hig.supervise.service.repository.MessageNoteRepository;
import com.isoftstone.hig.supervise.service.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShipperAppService {
    @Resource
    private ShipperRepository shipperRepository;
    @Resource
    private BusiMsgService busiMsgService;
    @Resource
    private MessageNoteRepository messageNoteRepository;

    /**
     * 分页查询
     */
    public ResultMode<ShipperListDTO> queryPage(PagingInfo<ShipperListQuery> query) {
        Page<SpvShipperInfoEntity> page = PageHelper.startPage(query.getCurrentPage(), query.getPageLength(), true);
        ShipperCondition condition = BeanUtil.copyProperties(query.getFilterModel(), ShipperCondition.class);
        List<SpvShipperInfoEntity> list = shipperRepository.queryList(condition);
        return ResultMode.successPageList(BeanUtil.copyToList(list, ShipperListDTO.class), (int) page.getTotal());
    }

    /**
     * 删除
     */
    public void delete(Long id) {
        shipperRepository.delete(id);
    }

    /**
     * 查看详情
     */
    public ShipperDetailDTO getDetail(Long id) {
        return BeanUtil.toBean(shipperRepository.getDetail(id), ShipperDetailDTO.class);
    }

    /**
     * 保存
     */
    public void save(ShipperCommand command) {
        SpvShipperInfoEntity entity = BeanUtil.toBean(command, SpvShipperInfoEntity.class);
        entity.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        shipperRepository.update(entity);
    }

    /**
     * 提交
     */
    public ResultMode submit(ShipperReportCommand command) {
        SpvShipperInfoEntity accountInfoEntity = shipperRepository.getDetail(command.getId());
        if (ObjUtil.isNull(accountInfoEntity)) {
            return ResultMode.fail(SpvErrorCode.BU_SPV_001.getMsg(), SpvErrorCode.BU_SPV_001.getCode());
        }
        if (StrUtil.equalsAny(accountInfoEntity.getStatus(), SuperviseEnmus.StatusEnum.DELETE.getCode())) {
            return ResultMode.fail(SpvErrorCode.BU_SPV_002.getMsg(), SpvErrorCode.BU_SPV_002.getCode());
        }

        List<MessageNote> messageNoteList = messageNoteRepository.queryReportingMessage(accountInfoEntity.getShipperId(), RabbitConstants.BusiType.WLHY_SHIPPER,
            command.getNetworkMainBodyIdList());
        if (CollUtil.isNotEmpty(messageNoteList)) {
            StringBuilder sb = new StringBuilder();
            List<String> reportingNetworkMainBodyNames = messageNoteList.stream().filter(messageNote -> "2".equals(messageNote.getMessageStatus())).map(MessageNote::getNetworkMainBodyName).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(reportingNetworkMainBodyNames)) {
                sb.append("网络货运主体[" + String.join(",", reportingNetworkMainBodyNames) + "]正在上报中.");
            }
            List<String> reportedNetworkMainBodyNames = messageNoteList.stream().filter(messageNote -> "1".equals(messageNote.getMessageStatus())).map(MessageNote::getNetworkMainBodyName).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(reportedNetworkMainBodyNames)) {
                sb.append("网络货运主体[" + String.join(",", reportedNetworkMainBodyNames) + "]已上报，请勿重复上报.");
            }
            if (sb.length() > 0) {
                return ResultMode.fail(sb.toString());
            }
        }

        //1、修改数据
        SpvShipperInfoEntity entity = BeanUtil.toBean(command, SpvShipperInfoEntity.class);
        entity.setStatus(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        shipperRepository.update(entity);
        // 2、上报
        ViewReportCommand viewReportCommand = new ViewReportCommand(command.getId(), command.getNetworkMainBodyIdList());
        ShipperInfoReportCommand reportCommand = getShipperInfoReportCommand(viewReportCommand);
        //设置网络货运主体
        ResultMode resultMode = busiMsgService.setNetworkId(reportCommand);
        if (!resultMode.isSucceed()) {
            return resultMode;
        }

        return busiMsgService.convertAndSend(reportCommand);
    }

    private ShipperInfoReportCommand getShipperInfoReportCommand(ViewReportCommand viewReportCommand) {
        SpvShipperInfoEntity entity = shipperRepository.getDetail(viewReportCommand.getId());
        ShipperInfoReportCommand reportCommand = BeanUtil.toBean(entity, ShipperInfoReportCommand.class);
        reportCommand.setBusiType(RabbitConstants.BusiType.WLHY_SHIPPER);
        reportCommand.setBusiId(entity.getShipperId());
        reportCommand.setNetworkMainBodyIdList(viewReportCommand.getNetworkMainBodyIdList());
        reportCommand.setNetworkMainBodyId(viewReportCommand.getNetworkMainBodyId());
        return reportCommand;
    }

    /**
     * 批量上报
     */
    public ResultMode batchReport(List<Long> idList) {
        idList.forEach(id -> {
            ShipperInfoReportCommand reportCommand = getShipperInfoReportCommand(new ViewReportCommand(id));
            busiMsgService.setNetworkId(reportCommand);
            busiMsgService.convertAndSend(reportCommand);
        });
        return ResultMode.success();
    }

    /**
     * 查看映射结果
     */
    public ResultMode<ShipperViewDTO> viewReportInfo(ViewReportInfoQuery query) {
        ViewReportCommand viewReportCommand = new ViewReportCommand(query.getId(), query.getNetworkMainBodyId());
        ShipperInfoReportCommand reportCommand = getShipperInfoReportCommand(viewReportCommand);

        ResultMode resultMode = busiMsgService.queryMappingResults(reportCommand);
        if (!resultMode.isSucceed()) {
            return ResultMode.success();
        }
        ShipperViewDTO viewDTO = (ShipperViewDTO) resultMode.getModel().get(0);
        return ResultMode.success(viewDTO);
    }

    /**
     * 上报回调
     */
    public void updateReportStatus(ReportMsgVO reportMsgVO) {
        if (StringUtils.isEmpty(reportMsgVO.getBusiId())) {
            return;
        }
        //查询托运人信息
        SpvShipperInfoEntity shipper = shipperRepository.getByShipperId(reportMsgVO.getBusiId());
        if (shipper == null) {
            log.info("updateReportStatus#托运人信息不存在:{}", reportMsgVO.getBusiId());
            return;
        }

        String status;
        //多渠道只要有一个上报失败，状态为上报失败
        if (shipper.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode()) ||
            reportMsgVO.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode())) {
            status = UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode();
        } else {
            //多渠道时，非上报失败，都为上报中状态
            status = UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode();
        }

        SpvShipperInfoEntity shipperUpd = new SpvShipperInfoEntity();
        shipperUpd.setId(shipper.getId());
        shipperUpd.setSendToProDateTime(reportMsgVO.getReportTime());
        shipperUpd.setStatus(status);
        //网络货运主体企业简称(code：message)
        shipperUpd.setSuccessMessage(reportMsgVO.getNetworkMainBodyName() + "(" + reportMsgVO.getResultInfo() + ")");
        shipperUpd.setSuccessMessage(StringUtils.substring(shipperUpd.getSuccessMessage(), 0, 255));
        shipperRepository.update(shipperUpd);
    }

}
