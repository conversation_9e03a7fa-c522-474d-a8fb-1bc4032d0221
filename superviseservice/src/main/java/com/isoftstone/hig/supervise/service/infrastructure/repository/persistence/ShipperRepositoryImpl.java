package com.isoftstone.hig.supervise.service.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.service.domain.model.condition.ShipperCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvShipperInfoEntity;
import com.isoftstone.hig.supervise.service.domain.repository.ShipperRepository;
import com.isoftstone.hig.supervise.service.infrastructure.repository.mapper.SpvShipperInfoMapper;
import com.isoftstone.hig.supervise.service.infrastructure.repository.po.SpvShipperInfoPO;
import com.isoftstone.hig.supervise.service.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ShipperRepositoryImpl implements ShipperRepository {
    @Resource
    private SpvShipperInfoMapper superviseShipperBusInfoMapper;

    @Override
    public void init(SpvShipperInfoEntity entity) {
        if(StrUtil.isEmpty(entity.getShipperCertNo())){
            return;
        }

        SpvShipperInfoPO po = BeanUtil.toBean(entity, SpvShipperInfoPO.class);
        entity.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        po.setLastUpdateTime(DateUtil.date());
        List<SpvShipperInfoEntity> list = queryList(new ShipperCondition(entity.getShipperCertNo()));
        if (CollUtil.isNotEmpty(list)) {
            po.setId(list.get(0).getId());
            superviseShipperBusInfoMapper.updateById(po);
        } else {
            po.setId(IdUtil.generateId());
            po.setStatus(UtilityEnum.SuperviseStatusEnum.TO_DO.getCode());
            po.setCreateTime(DateUtil.date());
            superviseShipperBusInfoMapper.insert(po);
        }


    }

    @Override
    public List<SpvShipperInfoEntity> queryList(ShipperCondition condition) {
        LambdaQueryWrapper<SpvShipperInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(condition.getShipperCertNo()), SpvShipperInfoPO::getShipperCertNo, condition.getShipperCertNo());
        queryWrapper.like(StrUtil.isNotBlank(condition.getShipperName()), SpvShipperInfoPO::getShipperName, condition.getShipperName());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getStatus()), SpvShipperInfoPO::getStatus, condition.getStatus());
        queryWrapper.ge(ObjUtil.isNotNull(condition.getStartSendToProDateTime()), SpvShipperInfoPO::getSendToProDateTime, condition.getStartSendToProDateTime());
        queryWrapper.le(ObjUtil.isNotNull(condition.getEndSendToProDateTime()), SpvShipperInfoPO::getSendToProDateTime, condition.getEndSendToProDateTime());
        queryWrapper.orderByDesc(SpvShipperInfoPO::getCreateTime);
        List<SpvShipperInfoPO> list = superviseShipperBusInfoMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(list, SpvShipperInfoEntity.class);
    }

    @Override
    public void delete(Long id) {
        SpvShipperInfoPO po = new SpvShipperInfoPO();
        po.setId(id);
        //设置为删除状态
        po.setStatus(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        po.setLastUpdaterId(JwtUtil.getInstance().getUserBaseIdByToken());
        po.setLastUpdateTime(DateUtil.date());
        superviseShipperBusInfoMapper.updateById(po);
    }

    @Override
    public SpvShipperInfoEntity getDetail(Long id) {
        SpvShipperInfoPO po = superviseShipperBusInfoMapper.selectById(id);
        return BeanUtil.toBean(po, SpvShipperInfoEntity.class);
    }

    @Override
    public void update(SpvShipperInfoEntity entity) {
        SpvShipperInfoPO po = BeanUtil.toBean(entity, SpvShipperInfoPO.class);
        po.setLastUpdaterId(JwtUtil.getInstance().getUserBaseIdByToken());
        po.setLastUpdateTime(DateUtil.date());
        superviseShipperBusInfoMapper.updateById(po);
    }

    @Override
    public SpvShipperInfoEntity getByShipperId(String shipperId) {
        if (StrUtil.isBlank(shipperId)) {
            return null;
        }
        LambdaQueryWrapper<SpvShipperInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpvShipperInfoPO::getShipperId, shipperId);
        return BeanUtil.toBean(superviseShipperBusInfoMapper.selectOne(queryWrapper), SpvShipperInfoEntity.class);
    }
}
