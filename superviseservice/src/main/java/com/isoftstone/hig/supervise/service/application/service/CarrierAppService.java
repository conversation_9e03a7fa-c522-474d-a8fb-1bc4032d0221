package com.isoftstone.hig.supervise.service.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.api.entity.MessageNote;
import com.isoftstone.hig.supervise.api.entity.ReportMsgVO;
import com.isoftstone.hig.supervise.api.service.BusiMsgService;
import com.isoftstone.hig.supervise.service.application.model.command.ActualCarrierInfoReportCommand;
import com.isoftstone.hig.supervise.service.application.model.command.CarrierCommand;
import com.isoftstone.hig.supervise.service.application.model.command.CarrierReportCommand;
import com.isoftstone.hig.supervise.service.application.model.command.ViewReportCommand;
import com.isoftstone.hig.supervise.service.application.model.dto.CarrierDetailDTO;
import com.isoftstone.hig.supervise.service.application.model.dto.CarrierListDTO;
import com.isoftstone.hig.supervise.service.application.model.dto.CarrierViewDTO;
import com.isoftstone.hig.supervise.service.application.model.query.CarrierListQuery;
import com.isoftstone.hig.supervise.service.application.model.query.ViewReportInfoQuery;
import com.isoftstone.hig.supervise.service.domain.model.condition.CarrierCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvActualCarrierInfoEntity;
import com.isoftstone.hig.supervise.service.domain.repository.CarrierRepository;
import com.isoftstone.hig.supervise.service.enmus.SuperviseEnmus;
import com.isoftstone.hig.supervise.service.infrastructure.exception.SpvErrorCode;
import com.isoftstone.hig.supervise.service.rabbitmq.RabbitConstants;
import com.isoftstone.hig.supervise.service.repository.MessageNoteRepository;
import com.isoftstone.hig.supervise.service.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CarrierAppService {
    @Resource
    private CarrierRepository carrierRepository;
    @Resource
    private BusiMsgService busiMsgService;

    @Resource
    private MessageNoteRepository messageNoteRepository;

    /**
     * 分页查询
     */
    public ResultMode<CarrierListDTO> queryPage(PagingInfo<CarrierListQuery> query) {
        Page<SpvActualCarrierInfoEntity> page = PageHelper.startPage(query.getCurrentPage(), query.getPageLength(), true);
        CarrierCondition condition = BeanUtil.copyProperties(query.getFilterModel(), CarrierCondition.class);
        List<SpvActualCarrierInfoEntity> list = carrierRepository.queryList(condition);
        return ResultMode.successPageList(BeanUtil.copyToList(list, CarrierListDTO.class), (int) page.getTotal());
    }

    /**
     * 删除
     */
    public void delete(Long id) {
        carrierRepository.delete(id);
    }

    /**
     * 查看详情
     */
    public CarrierDetailDTO getDetail(Long id) {
        return BeanUtil.toBean(carrierRepository.getDetail(id), CarrierDetailDTO.class);
    }

    /**
     * 保存
     */
    public ResultMode save(CarrierCommand command) {
        SpvActualCarrierInfoEntity accountInfoEntity = carrierRepository.getDetail(command.getId());
        if (ObjUtil.isNull(accountInfoEntity)) {
            return ResultMode.fail(SpvErrorCode.BU_SPV_001.getMsg(), SpvErrorCode.BU_SPV_001.getCode());
        }
        if (StrUtil.equalsAny(accountInfoEntity.getStatus(), SuperviseEnmus.StatusEnum.DELETE.getCode(),
            SuperviseEnmus.StatusEnum.REPORT_SUCCESS.getCode())) {
            return ResultMode.fail(SpvErrorCode.BU_SPV_002.getMsg(), SpvErrorCode.BU_SPV_002.getCode());
        }
        SpvActualCarrierInfoEntity entity = BeanUtil.toBean(command, SpvActualCarrierInfoEntity.class);
        entity.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        carrierRepository.update(entity);

        return ResultMode.success();
    }

    /**
     * 提交
     */
    public ResultMode submit(CarrierReportCommand command) {
        SpvActualCarrierInfoEntity accountInfoEntity = carrierRepository.getDetail(command.getId());
        if (ObjUtil.isNull(accountInfoEntity)) {
            return ResultMode.fail(SpvErrorCode.BU_SPV_001.getMsg(), SpvErrorCode.BU_SPV_001.getCode());
        }
        if (StrUtil.equalsAny(accountInfoEntity.getStatus(), SuperviseEnmus.StatusEnum.DELETE.getCode())) {
            return ResultMode.fail(SpvErrorCode.BU_SPV_002.getMsg(), SpvErrorCode.BU_SPV_002.getCode());
        }

        List<MessageNote> messageNoteList = messageNoteRepository.queryReportingMessage(command.getActualCarrierId(), RabbitConstants.BusiType.WLHY_ACTUAL_CARRIER,
            command.getNetworkMainBodyIdList());
        if (CollUtil.isNotEmpty(messageNoteList)) {
            List<String> networkMainBodyNames = messageNoteList.stream().filter(messageNote -> "2".equals(messageNote.getMessageStatus())).map(MessageNote::getNetworkMainBodyName).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(networkMainBodyNames)) {
                return ResultMode.fail("网络货运主体[" + String.join(",", networkMainBodyNames) + "]正在上报中.");
            }
        }

        //1、修改数据
        SpvActualCarrierInfoEntity entity = BeanUtil.toBean(command, SpvActualCarrierInfoEntity.class);
        entity.setStatus(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        carrierRepository.update(entity);
        // 2、上报
        ViewReportCommand viewReportCommand = new ViewReportCommand(command.getId(), command.getNetworkMainBodyIdList());
        ActualCarrierInfoReportCommand reportCommand = getActualCarrierInfoReportCommand(viewReportCommand);
        ResultMode resultMode = busiMsgService.setNetworkId(reportCommand);
        if (!resultMode.isSucceed()) {
            return resultMode;
        }

        return busiMsgService.convertAndSend(reportCommand);
    }

    private ActualCarrierInfoReportCommand getActualCarrierInfoReportCommand(ViewReportCommand viewReportCommand) {
        SpvActualCarrierInfoEntity entity = carrierRepository.getDetail(viewReportCommand.getId());
        ActualCarrierInfoReportCommand reportCommand = BeanUtil.toBean(entity, ActualCarrierInfoReportCommand.class);
        reportCommand.setBusiType(RabbitConstants.BusiType.WLHY_ACTUAL_CARRIER);
        reportCommand.setBusiId(entity.getActualCarrierId());
        reportCommand.setNetworkMainBodyIdList(viewReportCommand.getNetworkMainBodyIdList());
        reportCommand.setNetworkMainBodyId(viewReportCommand.getNetworkMainBodyId());
        return reportCommand;
    }

    /**
     * 批量上报
     */
    public ResultMode batchReport(List<Long> idList) {
        idList.forEach(id -> {
            ActualCarrierInfoReportCommand reportCommand = getActualCarrierInfoReportCommand(new ViewReportCommand(id));
            busiMsgService.setNetworkId(reportCommand);
            busiMsgService.convertAndSend(reportCommand);
        });
        return ResultMode.success();
    }

    /**
     * 查看映射结果
     */
    public ResultMode<CarrierViewDTO> viewReportInfo(ViewReportInfoQuery query) {
        ViewReportCommand viewReportCommand = new ViewReportCommand(query.getId(), query.getNetworkMainBodyId());
        ActualCarrierInfoReportCommand reportCommand = getActualCarrierInfoReportCommand(viewReportCommand);

        ResultMode resultMode = busiMsgService.queryMappingResults(reportCommand);
        if (!resultMode.isSucceed()) {
            return ResultMode.success();
        }
        CarrierViewDTO viewDTO = (CarrierViewDTO) resultMode.getModel().get(0);
        return ResultMode.success(viewDTO);
    }

    /**
     * 上报回调
     */
    public void updateReportStatus(ReportMsgVO reportMsgVO) {
        if (StringUtils.isEmpty(reportMsgVO.getBusiId())) {
            return;
        }
        //查询实际承运人信息
        SpvActualCarrierInfoEntity actualCarrier = carrierRepository.getByActualCarrierId(reportMsgVO.getBusiId());
        if (actualCarrier == null) {
            log.info("updateReportStatus#实际承运人信息不存在:{}", reportMsgVO.getBusiId());
            return;
        }

        String status;
        //多渠道只要有一个上报失败，状态为上报失败
        if (actualCarrier.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode()) ||
            reportMsgVO.getStatus().equals(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode())) {
            status = UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode();
        } else {
            //多渠道时，非上报失败，都为上报中状态
            status = UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode();
        }

        SpvActualCarrierInfoEntity actualCarrierUpd = new SpvActualCarrierInfoEntity();
        actualCarrierUpd.setId(actualCarrier.getId());
        actualCarrierUpd.setSendToProDateTime(reportMsgVO.getReportTime());
        actualCarrierUpd.setStatus(status);
        //网络货运主体企业简称(code：message)
        actualCarrierUpd.setSuccessMessage(reportMsgVO.getNetworkMainBodyName() + "(" + reportMsgVO.getResultInfo() + ")");
        actualCarrierUpd.setSuccessMessage(StringUtils.substring(actualCarrierUpd.getSuccessMessage(), 0, 255));
        carrierRepository.update(actualCarrierUpd);
    }

}
