package com.isoftstone.hig.supervise.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.isoftstone.hig.bms.api.client.BmsSuperviseClient;
import com.isoftstone.hig.common.model.PagingInfo;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.*;
import com.isoftstone.hig.common.utils.gson.WlydGsonUtils;
import com.isoftstone.hig.platform.api.entity.PlatformCmOperationMainBody;
import com.isoftstone.hig.platform.api.entity.PlatformCmPlatformParameter;
import com.isoftstone.hig.platform.api.entity.PlatformKafkaHandle;
import com.isoftstone.hig.platform.api.inter.PlatformCmOperationMainBodyInter;
import com.isoftstone.hig.supervise.api.entity.*;
import com.isoftstone.hig.supervise.api.filter.WaybillReportDto;
import com.isoftstone.hig.supervise.api.filter.WaybillSearchVo;
import com.isoftstone.hig.supervise.api.service.*;
import com.isoftstone.hig.supervise.service.enmus.*;
import com.isoftstone.hig.supervise.service.exchange.PlatformExchangeService;
import com.isoftstone.hig.supervise.service.pojo.ReportDTO;
import com.isoftstone.hig.supervise.service.pojo.SuperviseCapitalFlow;
import com.isoftstone.hig.supervise.service.pojo.SuperviseCapitalFlowPayment;
import com.isoftstone.hig.supervise.service.pojo.SuperviseCapitalFlowWaybill;
import com.isoftstone.hig.supervise.service.rabbitmq.RabbitConstants;
import com.isoftstone.hig.supervise.service.repository.*;
import com.isoftstone.hig.supervise.service.utils.IdUtil;
import com.isoftstone.hig.supervise.service.utils.SpvDateUtil;
import com.isoftstone.hig.supervise.service.utils.SpvSpringFactory;
import com.isoftstone.hig.supervise.service.utils.SuperviseConstants;
import com.isoftstone.hig.supervise.service.utils.exception.SupeStatusCodeEnum;
import com.isoftstone.hig.supervise.service.utils.exception.SupeWlydException;
import com.isoftstone.hig.tms.api.inter.TmsWaybillInter;
import com.isoftstone.hig.tms.api.query.TmsWaybillFilter;
import com.wanlianyida.dcs.entity.OrderReportingVo;
import com.wanlianyida.dcs.service.DcsWaybillService;
import com.wanlianyida.dcs.service.OrderReportingService;
import com.wanlianyida.dcs.vo.DcsWaybillDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.annotation.Resource;
import javax.xml.bind.JAXBException;

@Service
@Slf4j
public class WaybillServiceImpl implements WaybillService {

    @Resource
    WaybillBusinessRepository waybillBusinessRepository;
    @Resource
    WaybillAttributeBusinessRepository waybillAttributeBusinessRepository;
    @Resource
    WaybillOriginRepository waybillOriginRepository;
    @Resource
    WaybillAttributeOriginRepository waybillAttributeOriginRepository;

    @Resource
    private SuperviseCapitalFlowMapper superviseCapitalFlowMapper;

    @Resource
    private SuperviseCapitalFlowPaymentMapper superviseCapitalFlowPaymentMapper;

    @Resource
    private SuperviseCapitalFlowWaybillMapper superviseCapitalFlowWaybillMapper;

    @Resource
    MessageNoteService messageNoteService;

    @Resource
    private TmsWaybillInter tmsWaybillInter;

    @Autowired
    private KafkaUtil kafkaUtil;
    @Resource
    private BusiMsgService busiMsgService;

    @Resource
    private DriverService driverService;

    @Resource
    private VehicleService vehicleService;


    private final static String BMS_UPDATE_POOL_REPORT_STATUS = "bms_update_pool_report_status";

    @Resource
    VehicleBusinessRepository vehicleBusinessRepository;

    @Resource
    private ChannelConfigService channelConfigService;

    @Resource
    private EnumMappingService enumMappingService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private DcsWaybillService dcsWaybillService;

    @Resource
    private CapitalAccountService capitalAccountService;

    @Autowired(required=false)
    private PlatformCmOperationMainBodyInter platformCmOperationMainBodyInter;

    @Autowired
    private BmsSuperviseClient bmsSuperviseClient;

    @Autowired
    private OrderReportingService orderReportingService;

    @Resource
    DriverBusinessRepository driverBusinessRepository;

    @Resource
    private Executor asyncServiceExecutor;


    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void addWaybillInfo(Waybill waybill) throws Exception {
        //以上注释的代码逻辑已经抽取到tms中 rlj 0731
        ResultMode<Waybill> waybillMode = this.viewWaybillBusDetail(waybill);
        //做非空判断，避免数据重复同步
        if (waybillMode == null || CollectionUtils.isEmpty(waybillMode.getModel())) {
            //保存运单相关数据
            saveWaybillData(waybill);
            String waybillId = waybill.getOriginalDocumentNumber();
            //更新ES运单状态
            updateESWaybillStatus(waybillId, waybill.getStatus(), null);
            kafkaUtil.kafkaProducerSend(SpringContextUtil.getKafkaTopicPrefix() + BMS_UPDATE_POOL_REPORT_STATUS
                , waybillId);
            //上报司机和车辆
            reportVehicleAndDriverTran(waybill);
        }else {
            //如果没有上报成功，更新装卸货时间
            Waybill superviseWaybillInfo =  IterUtil.getFirst(waybillMode.getModel());
            if(!StrUtil.containsAny(superviseWaybillInfo.getStatus(),UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode(), UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode())){
                //更新运单信息
                Waybill updWaybill = new Waybill();
                updWaybill.setShippingNoteNumber(superviseWaybillInfo.getShippingNoteNumber());
                updWaybill.setConsignmentDateTimeTo(waybill.getConsignmentDateTimeTo());
                updWaybill.setGoodsReceiptDateTime(waybill.getGoodsReceiptDateTime());
                updWaybill.setDespatchActualDateTime(waybill.getDespatchActualDateTime());
                updWaybill.setTransportCosts(waybill.getTransportCosts()==null?new BigDecimal("0"):waybill.getTransportCosts());
                updWaybill.setTransportEndDate(waybill.getTransportEndDate());
                updWaybill.setTransportMileage(superviseWaybillInfo.getTransportMileage());
                updWaybill.setModifyDate(new Date());
                //根据渠道配置，调整运费金额
                handleTotalMonetaryAmount(waybill);
                updWaybill.setTotalMonetaryAmount(waybill.getTotalMonetaryAmount());

                WaybillAttribute updWaybillAttribute = new WaybillAttribute();
                updWaybillAttribute.setShippingNoteNumber(superviseWaybillInfo.getShippingNoteNumber());
                updWaybillAttribute.setGoodsReceiptDateTime(waybill.getGoodsReceiptDateTime());
                updWaybillAttribute.setDespatchActualDateTime(waybill.getDespatchActualDateTime());
                updWaybillAttribute.setSenderName(waybill.getWaybillAttribute().getSenderName());
                updWaybillAttribute.setSenderTel(waybill.getWaybillAttribute().getSenderTel());
                updWaybillAttribute.setRecverName(waybill.getWaybillAttribute().getRecverName());
                updWaybillAttribute.setRecverTel(waybill.getWaybillAttribute().getRecverTel());
                updWaybillAttribute.setCarryContractNo(waybill.getWaybillAttribute().getCarryContractNo());
                updWaybillAttribute.setShipContractNo(waybill.getWaybillAttribute().getShipContractNo());
                updWaybill.setWaybillAttribute(updWaybillAttribute);

                updateWaybillInfo(updWaybill);
                //上报司机和车辆
                reportVehicleAndDriverTran(superviseWaybillInfo);
            }
            String waybillId = waybill.getOriginalDocumentNumber();
            kafkaUtil.kafkaProducerSend(SpringContextUtil.getKafkaTopicPrefix() + BMS_UPDATE_POOL_REPORT_STATUS
                , waybillId);
        }
        //更新supervise_vehicle_bus_info表的实际承运人名称、实际承运人id todo 判断是否江苏监管
        vehicleService.updateVehicleActualCarrier(waybill.getVehicleNumber(),waybill.getWaybillAttribute().getActualCarrierName(),waybill.getWaybillAttribute().getActualCarrierId());
    }

    /**
     * 上报司机和车辆
     */
    private void reportVehicleAndDriverTran(Waybill waybill) {
        //事务提交之后在执行
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    asyncServiceExecutor.execute(() -> reportVehicleAndDriver(waybill));
                }
            });
        } else {
            asyncServiceExecutor.execute(() -> reportVehicleAndDriver(waybill));
        }
    }

    /**
     * 上报司机和车辆
     */
    private void reportVehicleAndDriver(Waybill waybill) {
        log.info("reportVehicleAndDriver#start, waybill:{}", waybill.getShippingNoteNumber());
        List<String> autoReportCustIds = channelConfigService.getAutoReportCustIds();
        if (IterUtil.isEmpty(autoReportCustIds) || !autoReportCustIds.contains(waybill.getNetworkMainBodyId())) {
            log.info("reportVehicleAndDriver#未开启自动上报, waybill:{}", waybill.getShippingNoteNumber());
            return;
        }
        ReportDTO reportDTO = differentiateWaybillProcessing(Arrays.asList(waybill), waybill.getNetworkMainBodyId());
        if (ObjUtil.isNull(reportDTO)) {
            return;
        }
        //上报车辆
        if (IterUtil.isNotEmpty(reportDTO.getReportVehicleList())) {
            log.info("reportVehicleAndDriver#上报车辆: {}", JSONUtil.toJsonStr(reportDTO.getReportVehicleList()));
            vehicleService.reportVehicleList(reportDTO.getReportVehicleList());
        }
        //上报司机
        if (IterUtil.isNotEmpty(reportDTO.getReportDrivingList())) {
            log.info("reportVehicleAndDriver#上报司机: {}", JSONUtil.toJsonStr(reportDTO.getReportDrivingList()));
            driverService.reportDriverList(reportDTO.getReportDrivingList());
        }
        log.info("reportVehicleAndDriver#end, waybill:{}", waybill.getShippingNoteNumber());
    }

    /**
     * 处理单位承运人名称
     * @param waybill 运单
     */
    private void handleCarrier(Waybill waybill){
        SuperviseChannelConfig config = channelConfigService.getConfigByCustId(waybill.getNetworkMainBodyId());
        //针对需要修改实际承运人
        if(ObjectUtil.isNotNull(config) && !StrUtil.equals(config.getHandleCarrierType(), HandleCarrierTypeEnum.TEN.getCode())) {
            log.info("#handleCarrier#运单网络货运主体id："+waybill.getNetworkMainBodyId());
            // 查询实际承运人并且录入
            String vehicleNumber = waybill.getWaybillAttribute().getVehicleNumber();
            String actualCarrierName = waybill.getWaybillAttribute().getActualCarrierName();
            String colorCode = waybill.getWaybillAttribute().getVehiclePlateColorCode();
            Vehicle vehicle = new Vehicle();
            vehicle.setVehicleNumber(vehicleNumber);
            vehicle.setVehiclePlateColorCode(colorCode);
            vehicle = vehicleService.queryByNumberColor(vehicle);
            if (ObjectUtil.isNotNull(vehicle)) {
                if (StrUtil.equals(config.getHandleCarrierType(), HandleCarrierTypeEnum.TWENTY.getCode())) {
                    if (StrUtil.isEmpty(vehicle.getIndividualFlag())) {
                        log.info("无个人企业标识，承运人名称无法处理设置为空，需要人工处理,车牌号：{},颜色：{}", vehicleNumber, colorCode);
                        //waybill.getWaybillAttribute().setActualCarrierName("");
                    }else {
                        if (vehicle.getIndividualFlag().equals(IndividualFlagEnum.IndividualFlagCode.ENTERPRISE.getCode())) {
                            waybill.getWaybillAttribute().setActualCarrierName(vehicle.getOwner() + "(" + actualCarrierName + ")");
                        } else {
                            //waybill.getWaybillAttribute().setActualCarrierName(vehicle.getOwner());
                        }
                    }
                } else if (StrUtil.equals(config.getHandleCarrierType(), HandleCarrierTypeEnum.THIRTY.getCode())) {
                    waybill.getWaybillAttribute().setActualCarrierName(vehicle.getOwner());
                }
            }else {
                log.info("无车辆信息时，承运人名称无法处理设置为空，需要人工处理。,车牌号：{},颜色：{}", vehicleNumber, colorCode);
                //waybill.getWaybillAttribute().setActualCarrierName("");
            }
        }
        //tob 承运人
        String waybillId = waybill.getOriginalDocumentNumber();
        OrderReportingVo orderReportingVo = orderReportingService.getValue(waybillId);
        if(ObjUtil.isNotNull(orderReportingVo) && StrUtil.equals(orderReportingVo.getValue()+"","20")){
            waybill.getWaybillAttribute().setActualCarrierName(waybill.getWaybillAttribute().getActualCarrierName());
        }

    }

    /**
     * 设置实际承运人道路运输经营许可证号
     *
     * @param waybill 运单
     */
    /*private void setActualCarrierBusinessLicense(Waybill waybill) {
        try {
            if (waybill == null || waybill.getWaybillAttribute() == null) {
                return;
            }
            String actualCarrierBusinessLicense = waybill.getWaybillAttribute().getActualCarrierBusinessLicense();
            if (StrUtil.isNotBlank(actualCarrierBusinessLicense)) {
                return;
            }

            String vehicleNumber = waybill.getWaybillAttribute().getVehicleNumber();
            if (StrUtil.isBlank(vehicleNumber)) {
                return;
            }

            List<TcsCarTransportCertificateVO> voList = tcsExchangeService.queryCarLicenseListByCarPlateNo(vehicleNumber);
            if (IterUtil.isEmpty(voList)) {
                log.info("setActualCarrierBusinessLicense#未查询到车辆道路运输证信息,vehicleNumber:{}", vehicleNumber);
                return;
            }
            String carColor = "";
            for (TcsCarTransportCertificateVO certificateVO : voList) {
                *//**
                 * 先按车牌+颜色匹配经营许可证号，如果匹配不到，就随便取一个
                 *//*
                waybill.getWaybillAttribute().setActualCarrierBusinessLicense(certificateVO.getLicenseNo());
                carColor = certificateVO.getPlateColor() == null ? "" : certificateVO.getPlateColor().toString();
                if (StrUtil.equals(waybill.getWaybillAttribute().getVehiclePlateColorCode(), carColor)) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("setActualCarrierBusinessLicense#设置道路许可证号异常：{}", e);
        }
    }*/

    /**
     * 设置运输里程
     *
     * @param waybill 运单
     */
    private void setTransportMileage(Waybill waybill){
        try {
            if(waybill.getTransportMileage()!=null && waybill.getTransportMileage().intValue()>0){
                return;
            }
            log.info("获取运输里程-运单号：{}", waybill.getShippingNoteNumber());
            TmsWaybillFilter waybillFilter = new TmsWaybillFilter();
            waybillFilter.setWaybillId(waybill.getShippingNoteNumber());
            ResultMode resultMode = tmsWaybillInter.getWabybillTransportMileage(waybillFilter);
            log.info("获取运输里程：{}-{}",waybill.getShippingNoteNumber(), JSONUtil.toJsonStr(resultMode));
            if(resultMode==null || IterUtil.isEmpty(resultMode.getModel())){
                return;
            }
            Object transportMileage = resultMode.getModel().get(0);
            log.info("运输里程：{}-{}", waybill.getShippingNoteNumber(), transportMileage);
            if(transportMileage==null){
                return;
            }
            waybill.setTransportMileage(Integer.parseInt(String.valueOf(transportMileage)));
        } catch (Exception e) {
            log.error("设置运输里程异常：{}",e);
        }
    }


    private void saveWaybillData(Waybill waybill) {
        // bus&rep表保存的统一社会信用代码，要存真实的
//        waybill.setUnifiedSocialCreditIdentifier(getUnifiedSocialCreditCodeRealByOld(waybill.getUnifiedSocialCreditIdentifier()));
//        waybill.setConsigneeId(getUnifiedSocialCreditCodeRealByOld(waybill.getConsigneeId()));
//        waybill.setConsignorId(getUnifiedSocialCreditCodeRealByOld(waybill.getConsignorId()));

        //设置道路运输经营许可证号
//        setActualCarrierBusinessLicense(waybill);

        //设置运输里程
        setTransportMileage(waybill);

        waybillOriginRepository.addWaybill(waybill);
        waybillAttributeOriginRepository.addWaybillAttribute(waybill.getWaybillAttribute());

        //设置车辆颜色（同步成与监管车辆信息一致）
        setVehicleColor(waybill);

        //设置挂车车牌和颜色
        setTrailerInfo(waybill);

        //根据渠道配置，调整货物项毛重
        handleGoodsInfo(waybill);

        //根据渠道配置，调整运费金额
        handleTotalMonetaryAmount(waybill);

        waybill.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        // 运单bus表处理承运人名称，pre数据不变
        handleCarrier(waybill);
        //支付状态
        handlePayStatus(waybill);
        waybillBusinessRepository.addWaybill(waybill);
        WaybillAttribute waybillAttribute = waybill.getWaybillAttribute();
        // waybillAttribute.setActualCarrierName(waybillAttribute.getDriverName());
        // waybillAttribute.setActualCarrierId(waybillAttribute.getDrivingLicense());
        if (StrUtil.isBlank(waybillAttribute.getInsuranceCompanyCode())) {
            waybillAttribute.setInsuranceCompanyCode("none");
        }
        waybillAttribute.setStatisticsType(2);
        if (StrUtil.isBlank(waybillAttribute.getInsuranceAccessUrl())) {
            waybillAttribute.setInsuranceAccessUrl("none");
        }
        waybillAttributeBusinessRepository.addWaybillAttribute(waybillAttribute);
    }

    private void handlePayStatus(Waybill waybill) {
        //如果资金已存在，设置为支付成功
        CapitalAccount capitalAccount = new CapitalAccount();
        capitalAccount.setShippingNoteNumber(waybill.getShippingNoteNumber());
        ResultMode<CapitalAccount> resultMode = capitalAccountService.getCapitalAccountDetail(capitalAccount);
        if (ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && IterUtil.isNotEmpty(resultMode.getModel())) {
            waybill.setPayStatus("20");
            return;
        }
        waybill.setPayStatus("10");
    }

    /**
     * 调整运费金额
     */
    private void handleTotalMonetaryAmount(Waybill waybill) {
        SuperviseChannelConfig config = channelConfigService.getConfigByCustId(waybill.getNetworkMainBodyId());
        if (ObjectUtil.isNotEmpty(config) && StrUtil.equals(config.getAmountType(), WaybillEnmu.AmountTypeEnum.TWENTY.getCode())) {
            //运费金额
            BigDecimal receivableTotalCost = waybill.getReceivableTotalCost();
            log.info("运费金额#receivableTotalCost:{}", receivableTotalCost);
            if (ObjectUtil.isNotEmpty(receivableTotalCost)) {
                DecimalFormat df = new DecimalFormat("0.000");
                waybill.setTotalMonetaryAmount(df.format(receivableTotalCost));
            }
        }
    }

    /**
     * 调整货物项
     *
     * @param waybill
     * @return void
     * <AUTHOR>
     * @Date 2023/8/4 16:10
     */
    private void handleGoodsInfo(Waybill waybill) {
        SuperviseChannelConfig config = channelConfigService.getConfigByCustId(waybill.getNetworkMainBodyId());
        if (ObjectUtil.isNotEmpty(config) && StrUtil.equals(config.getWeightType(), WaybillEnmu.WeightTypeEnum.TWO.getCode())) {
            // 货物项毛重为派车重量
            BigDecimal transportWeight = waybill.getTransportWeight();
            if (ObjectUtil.isNotEmpty(transportWeight)) {
                DecimalFormat df = new DecimalFormat("0.000");
                String format = df.format(transportWeight.setScale(3, BigDecimal.ROUND_HALF_UP));
                waybill.getWaybillAttribute().setGoodsItemGrossWeight(format);
            }
        }
    }

    /**
     * 设置车辆颜色（同步成与监管车辆信息一致）
     *
     * @param waybill
     * @return void
     * <AUTHOR>
     * @Date 2023/8/2 17:16
     */
    private void setVehicleColor(Waybill waybill) {
//        if (StrUtil.isNotBlank(waybill.getWaybillAttribute().getVehiclePlateColorCode())) {
//            return;
//        }
        String vehicleNumber = waybill.getWaybillAttribute().getVehicleNumber();
        try {
            ResultMode<Vehicle> resultMode = vehicleService.viewVehicleBusDetail(vehicleNumber, null);
            log.info("WaybillServiceImpl#setVehicleColor，vehicleNumber：{}，result：{}",vehicleNumber, JSONUtil.toJsonStr(resultMode));
            if (ObjectUtil.isNotEmpty(resultMode) && IterUtil.isNotEmpty(resultMode.getModel())) {
                List<String> colorList = resultMode.getModel().stream().filter(vehicle -> StrUtil.isNotBlank(vehicle.getVehiclePlateColorCode()))
                    .map(Vehicle::getVehiclePlateColorCode).collect(Collectors.toList());
                //运单上的车牌颜色在监管中不存在，默认取第一个车牌颜色
                if (IterUtil.isNotEmpty(colorList) && !colorList.contains(waybill.getWaybillAttribute().getVehiclePlateColorCode())) {
                    String color = IterUtil.getFirst(colorList);
                    waybill.getWaybillAttribute().setVehiclePlateColorCode(color);
                }
//                    else if (StrUtil.isNotBlank(waybill.getWaybillAttribute().getVehiclePlateColorCode())) {
//                        //监管车辆没有颜色，更新运单车辆颜色到车辆
//                        updateVehicleColor(vehicleNumber, waybill.getWaybillAttribute().getVehiclePlateColorCode());
//                    }
            }
        } catch (Exception e) {
            log.error("运单设置车辆颜色，查询车辆详情异常：", e);
        }
    }

    /**
     * 设置挂车车牌和颜色
     *
     * @param waybill
     * @return void
     * <AUTHOR>
     * @Date 2023/7/31 15:20
     */
    private void setTrailerInfo(Waybill waybill) {
        WaybillAttribute waybillAttribute = waybill.getWaybillAttribute();
        //运单中存在挂车
        String trailerPlateNumber = waybillAttribute.getTrailerVehiclePlateNumber();
        if (StrUtil.isNotBlank(trailerPlateNumber)) {
            //去监管车辆中查询挂车颜色（同步成与监管车辆信息一致）
            boolean flag = setTrailerColorBySupervise(trailerPlateNumber, waybillAttribute);
            /*if (flag) {
                return;
            }

            //去tcs查询挂车颜色
            flag = setTrailerColorByTcs(trailerPlateNumber, waybillAttribute);
            if (flag) {
                // 同步运单上的挂车颜色到监管牵引车
//                updateVehicleColor(trailerPlateNumber, waybillAttribute.getTrailerVehiclePlateColorCode());
                return;
            }*/
        } else {
            //运单中没有挂车，查询运单上车辆的默认挂车
            String vehicleNumber = waybillAttribute.getVehicleNumber();
            if (StrUtil.isNotBlank(vehicleNumber)) {
                //查询监管中的车辆信息
                boolean flag = setTrailerInfoBySupervise(vehicleNumber, waybillAttribute);
                /*if (flag) {
                    return;
                }

                //监管车辆没有默认挂车，去platform获取
                flag = setTrailerInfoByPlatform(vehicleNumber, waybillAttribute);
                if (flag) {
                    // 同步运单上的挂车到监管牵引车
//                    updateTrailerInVehicle(waybillAttribute);
                    return;
                }

                //platform没有默认挂车，到cmd随机取本省的一个挂车
                flag = setTrailerInfoByCmd(vehicleNumber, waybillAttribute);
                if (flag) {
                    // 同步运单上的挂车到监管牵引车
//                    updateTrailerInVehicle(waybillAttribute);
                    return;
                }*/
            }
        }
    }

    /**
     * 去tcs查询挂车颜色
     *
     * @param trailerPlateNumber
     * @param waybillAttribute
     * @return boolean
     * <AUTHOR>
     * @Date 2023/8/2 17:21
     */
    /*private boolean setTrailerColorByTcs(String trailerPlateNumber, WaybillAttribute waybillAttribute) {
        List<TcsCarVO> tcsCarVOS = tcsExchangeService.queryBasicInfoByCarPlateNo(trailerPlateNumber);
        if (IterUtil.isEmpty(tcsCarVOS)) {
            log.info("WaybillServiceImpl#setTrailerColorByTcs，未获取到挂车信息，trailerPlateNumber：{}", trailerPlateNumber);
            return false;
        }

        TcsCarVO tcsCarVO = IterUtil.getFirst(tcsCarVOS);
        waybillAttribute.setTrailerVehiclePlateColorCode(StrUtil.toStringOrNull(tcsCarVO.getPlateColor()));
        return true;
    }*/

    /**
     * 去监管车辆中查询挂车颜色
     *
     * @param trailerPlateNumber
     * @param waybillAttribute
     * @return boolean
     * <AUTHOR>
     * @Date 2023/8/2 17:21
     */
    private boolean setTrailerColorBySupervise(String trailerPlateNumber, WaybillAttribute waybillAttribute) {
        try {
            ResultMode<Vehicle> resultMode = vehicleService.viewVehicleBusDetail(trailerPlateNumber, null);
            log.info("WaybillServiceImpl#setTrailerColorBySupervise，trailerPlateNumber：{}，result：{}",trailerPlateNumber, JSONUtil.toJsonStr(resultMode));
            if (ObjectUtil.isNotEmpty(resultMode) && IterUtil.isNotEmpty(resultMode.getModel())) {
                List<String> colorList = resultMode.getModel().stream().filter(vehicle -> StrUtil.isNotBlank(vehicle.getVehiclePlateColorCode()))
                    .map(Vehicle::getVehiclePlateColorCode).collect(Collectors.toList());
                //运单上的挂车车牌颜色在监管中不存在，默认取第一个车牌颜色
                if (IterUtil.isNotEmpty(colorList)) {
                    String trailerVehiclePlateColorCode = waybillAttribute.getTrailerVehiclePlateColorCode();
                    if (StrUtil.isNotBlank(trailerVehiclePlateColorCode) && colorList.contains(trailerVehiclePlateColorCode)) {
                        return true;
                    }
                    String color = IterUtil.getFirst(colorList);
                    waybillAttribute.setTrailerVehiclePlateColorCode(color);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("运单设置挂车颜色，查询车辆详情异常：", e);
        }

        return false;
    }

    /**
     * 去监管车辆获取车辆默认挂车
     *
     * @param vehicleNumber
     * @param waybillAttribute
     * @return boolean
     * <AUTHOR>
     * @Date 2023/8/1 18:18
     */
    private boolean setTrailerInfoBySupervise(String vehicleNumber, WaybillAttribute waybillAttribute) {
        try {
            ResultMode<Vehicle> vehicleResultMode = vehicleService.viewVehicleBusDetail(vehicleNumber, null);
            log.info("WaybillServiceImpl#viewVehicleBusDetail，vehicleNumber：{}，result：{}",vehicleNumber, JSONUtil.toJsonStr(vehicleResultMode));
            if (ObjectUtil.isNotEmpty(vehicleResultMode) && IterUtil.isNotEmpty(vehicleResultMode.getModel())) {
                Vehicle vehicle = IterUtil.getFirst(vehicleResultMode.getModel());
                if (ObjectUtil.isNotEmpty(vehicle) && !StrUtil.startWith(vehicle.getVehicleType(), "Q")) {
                    //非牵引车，不需要挂车信息
                    return true;
                }
                if (ObjectUtil.isNotEmpty(vehicle) && StrUtil.isNotBlank(vehicle.getTrailerVehiclePlateNumber())) {
                    waybillAttribute.setTrailerVehiclePlateNumber(vehicle.getTrailerVehiclePlateNumber());
                    String trailerColor = vehicle.getTrailerVehiclePlateColorCode();
                    waybillAttribute.setTrailerVehiclePlateColorCode(StrUtil.blankToDefault(trailerColor, "1"));
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("运单设置挂车车牌和颜色，查询车辆详情异常：", e);
        }

        return false;
    }

    /**
     * 去cmd获取车辆默认挂车
     *
     * @param vehicleNumber
     * @param waybillAttribute
     * @return boolean
     * <AUTHOR>
     * @Date 2023/8/1 18:18
     */
    /*private boolean setTrailerInfoByCmd(String vehicleNumber, WaybillAttribute waybillAttribute) {
        PagingInfo<CmdCarFilter> pagingInfo = new PagingInfo<>();
        CmdCarFilter cmdCarFilter = new CmdCarFilter();
        cmdCarFilter.setCarPlateNo(vehicleNumber);
        pagingInfo.setFilterModel(cmdCarFilter);
        pagingInfo.setCurrentPage(0);
        pagingInfo.setPageLength(1);
        ResultMode<CmdCar> cmdCarResultMode = cmdCarInter.queryByCondition(pagingInfo);
        log.info("WaybillServiceImpl#queryByCondition param：{}，result：{}",JSONUtil.toJsonStr(pagingInfo), JSONUtil.toJsonStr(cmdCarResultMode));
        if (ObjectUtil.isNotEmpty(cmdCarResultMode) && IterUtil.isNotEmpty(cmdCarResultMode.getModel())) {
            CmdCar cmdCar = IterUtil.getFirst(cmdCarResultMode.getModel());
            if (ObjectUtil.isNotEmpty(cmdCar)) {
                PlatformUmCar platformUmCar = new PlatformUmCar();
                platformUmCar.setCarRegion(cmdCar.getCarRegion());
                //获取司机id
                if (StrUtil.isNotBlank(waybillAttribute.getDrivingLicense())) {
                    String drivingLicense = waybillAttribute.getDrivingLicense();
                    Driver driver = driverService.queryDriverBusInfoByDrivingLicense(drivingLicense);
                    log.info("WaybillServiceImpl#queryDriverBusInfoByDrivingLicense param：{}，result：{}",JSONUtil.toJsonStr(drivingLicense), JSONUtil.toJsonStr(driver));
                    if (ObjectUtil.isNotEmpty(driver)) {
                        platformUmCar.setDriverId(driver.getDriverId());
                    }
                }
                ResultMode<PlatformUmCar> trailerFromCmd = platformUmCarInter.getRandomTrailerFromCmd(platformUmCar);
                log.info("WaybillServiceImpl#getRandomTrailerFromCmd param：{}，result：{}",JSONUtil.toJsonStr(platformUmCar), JSONUtil.toJsonStr(trailerFromCmd));
                if (ObjectUtil.isNotEmpty(trailerFromCmd) && IterUtil.isNotEmpty(trailerFromCmd.getModel())) {
                    PlatformUmCar cmdTrailer = IterUtil.getFirst(trailerFromCmd.getModel());
                    if (ObjectUtil.isNotEmpty(cmdTrailer)) {
                        waybillAttribute.setTrailerVehiclePlateNumber(cmdTrailer.getCarPlateNo());
                        String trailerColor = StrUtil.toStringOrNull(cmdTrailer.getCarColor());
                        waybillAttribute.setTrailerVehiclePlateColorCode(StrUtil.blankToDefault(trailerColor, "1"));
                        return true;
                    }
                }
            }
        }
        return false;
    }*/

    /**
     * 去platform获取车辆默认挂车
     *
     * @param vehicleNumber
     * @param waybillAttribute
     * @return boolean
     * <AUTHOR>
     * @Date 2023/8/1 18:16
     */
    /*private boolean setTrailerInfoByPlatform(String vehicleNumber, WaybillAttribute waybillAttribute) {
        ArrayList<String> carNoList = new ArrayList<>();
        carNoList.add(vehicleNumber);
        ResultMode<PlatformUmCar> umCarResultMode = platformUmCarInter.platformUmCarForCmdByTwenty(carNoList);
        log.info("WaybillServiceImpl#platformUmCarForCmdByTwenty，vehicleNumber：{}，result：{}",vehicleNumber, JSONUtil.toJsonStr(umCarResultMode));
        if (ObjectUtil.isNotEmpty(umCarResultMode) && IterUtil.isNotEmpty(umCarResultMode.getModel())) {
            PlatformUmCar platformUmCar = IterUtil.getFirst(umCarResultMode.getModel());
            if (ObjectUtil.isNotEmpty(platformUmCar) && StrUtil.isNotBlank(platformUmCar.getDefaultTrailer())) {
                //查询挂车是否审核通过（在cmd中存在）
                CmdCarFilter cmdCarFilter = new CmdCarFilter();
                cmdCarFilter.setCarPlateNo(platformUmCar.getDefaultTrailer());
                ResultMode<Integer> countByCondition = cmdCarInter.countByCondition(cmdCarFilter);
                if (ObjectUtil.isNotEmpty(countByCondition) && IterUtil.isNotEmpty(countByCondition.getModel())) {
                    Integer count = Optional.ofNullable(IterUtil.getFirst(countByCondition.getModel())).orElse(0);
                    if (count > 0) {
                        waybillAttribute.setTrailerVehiclePlateNumber(platformUmCar.getDefaultTrailer());
                        if (StrUtil.isEmpty(platformUmCar.getDefaultTrailer())){
                            return false;
                        }
                        String defaultTrailerColor = StrUtil.toStringOrNull(platformUmCar.getDefaultTrailerColor());
                        waybillAttribute.setTrailerVehiclePlateColorCode(StrUtil.blankToDefault(defaultTrailerColor, "1"));
                        return true;
                    }
                }
            }
        }

        return false;
    }*/

    /*private String getUnifiedSocialCreditCodeRealByOld(String unifiedSocialCreditCode){
        log.info(" 获取真实信用代码 :getRealUnifiedSocialCreditCodeByOld ,入参公司社会信用代码 :{}",unifiedSocialCreditCode);
        String unifiedSocialCreditCodeReal = unifiedSocialCreditCode;
        PlatformUmCompanyFilter filter = new PlatformUmCompanyFilter();
        filter.setSocialCreditCode(unifiedSocialCreditCodeReal);
        ResultMode<PlatformUmCompany> companyResult = platformUmCompanyInter.getCompanyDetailByCondition(filter);

        if (companyResult !=null && companyResult.getModel() !=null && companyResult.getModel().size() > 0){
            log.info("companyResult:{}", WlydGsonUtils.convertObjToJSONStr(companyResult));
            PlatformUmCompany company = companyResult.getModel().get(0);
            if(company!=null) {
                unifiedSocialCreditCodeReal = company.getSocialCreditCodeReal();
            }
        }else{
            log.info(" 公司社会信用代码:{},获取公司信息为空",unifiedSocialCreditCodeReal);
        }

        return unifiedSocialCreditCodeReal;
    }*/

    @Override
    public ResultMode<Waybill> qureyWaybillBusPageInfo(PagingInfo<WaybillSearchVo> pageInfo) throws Exception {
        log.info("进入 WaybillServiceImpl.qureyWaybillBusPageInfo  方法中查询参数{}", JSON.toJSONString(pageInfo));
        WaybillSearchVo filterModel = pageInfo.getFilterModel();
        //如果没有车牌条件，单独count
        boolean isCount = true;
        int count = 0;
        if (StrUtil.isBlank(filterModel.getVehicleNumber())) {
            isCount = false;
            count = waybillBusinessRepository.countWaybillPageInfoBySearchCondition(filterModel);
            if (count == 0) {
                return ResultMode.success();
            }
        }

        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength, isCount);
        // todo V4.7天津监管异常处理 需求优化地
        List<Waybill> list = waybillBusinessRepository.queryWaybillPageInfoBySearchCondition(pageInfo);
        log.info("进入 waybillBusinessRepository.queryWaybillPageInfoBySearchCondition方法返回结果{}", JSON.toJSONString(list));
        PageInfo<Waybill> returnPageInfo = new PageInfo<Waybill>(list);
        ResultMode<Waybill> mode = new ResultMode<>();
        if (isCount) {
            mode.setTotal((int) returnPageInfo.getTotal());
        } else {
            mode.setTotal(count);
        }
        mode.setModel(returnPageInfo.getList());

        return mode;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public ResultMode<Waybill> deleteWaybillInfo(Waybill waybill) throws Exception {

        ResultMode<Waybill> model = new ResultMode<>();

        //校验数据是否可以删除
        WaybillSearchVo vo = new WaybillSearchVo();

        List<String> idList = new ArrayList<String>();
        idList.add(waybill.getShippingNoteNumber());

        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode());
        vo.setIdList(idList);
        vo.setStatusList(statusList);
        List<String> searchResult = searchExistsData(vo);
        if (searchResult != null && searchResult.size() > 0) {  ///有数据代表着可以删除
            //设置为删除状态
            waybill.setStatus(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
            //设置修改时间
            waybill.setModifyDate(new Date());
            waybill.setModifyBy(JwtUtil.getInstance().getUserBaseIdByToken());
            log.info("进入 WaybillServiceImpl.deleteWaybillInfo  方法中查询参数{}", JSON.toJSONString(waybill));
            waybillBusinessRepository.update(waybill);
            //更新ES运单状态
            updateESWaybillStatus(waybill.getShippingNoteNumber(), waybill.getStatus(), null);
        } else {
            model.setErrMsg("该电子运单信息不能操作！");
            model.setSucceed(false);
        }

        return model;
    }

    //保存可以重复保存
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public ResultMode<Waybill> saveWaybillInfo(Waybill waybill) throws Exception {

        ResultMode<Waybill> model = new ResultMode<>();

        //校验数据是否可以保存
        WaybillSearchVo vo = new WaybillSearchVo();

        List<String> idList = new ArrayList<String>();
        idList.add(waybill.getShippingNoteNumber());

        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
        vo.setIdList(idList);
        vo.setStatusList(statusList);
        List<String> searchResult = searchExistsData(vo);
        if (searchResult != null && searchResult.size() > 0) {  ///有数据代表着可以保存
            //设置为保存状态
            String oldStatus = waybill.getStatus();
            waybill.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());

            //保存运单信息
            saveWaybillBusInfo(waybill);

            //更新ES运单状态
            updateESWaybillStatus(waybill.getShippingNoteNumber(), waybill.getStatus(), oldStatus);
        } else {
            model.setErrMsg("该电子运单信息不能操作！");
            model.setSucceed(false);
        }

        return model;
    }

    /**
     * 更新运单豪华版
     * @param waybill
     */
    public int updateWaybillInfo(Waybill waybill){
        //转换小数位
        formatWeight(waybill);
        int update1 = waybillBusinessRepository.updateByNull(waybill);
        //更新info数据，同时更新attribute
        copyToAttribute(waybill);
        int update2 = waybillAttributeBusinessRepository.update(waybill.getWaybillAttribute());

        if (update1 <= 0 || update2 <= 0) {
            log.info("updateWaybillInfo#更新运单失败，waybill：{}", JSONUtil.toJsonStr(waybill));
            return 0;
        }

        return 1;
    }

    private void copyToAttribute(Waybill waybill) {
        //收货地址
        if (StrUtil.isNotBlank(waybill.getGoodsReceiptPlace())) {
            waybill.getWaybillAttribute().setGoodsReceiptPlace(waybill.getGoodsReceiptPlace());
        }
        //装货地址
        if (StrUtil.isNotBlank(waybill.getPlaceOfLoading())) {
            waybill.getWaybillAttribute().setPlaceOfLoading(waybill.getPlaceOfLoading());
        }
        //保险公司代码 为空转换
        if (StrUtil.isBlank(waybill.getWaybillAttribute().getInsuranceCompanyCode())) {
            waybill.getWaybillAttribute().setInsuranceCompanyCode("none");
        }
    }

    @Override
    public ResultMode<String> submitWaybill(Waybill waybill){

        ResultMode<String> model = new ResultMode<>();
        Waybill temp = waybillBusinessRepository.viewWaybillBusInfo(waybill);
        if(temp==null){
            return ResultMode.fail("上报运单信息["+waybill.getShippingNoteNumber()+"]没有找到");
        }
        if (UtilityEnum.SuperviseStatusEnum.DELETE.getCode().equals(temp.getStatus())) {
            model.setErrMsg("该电子运单信息已删除，不能操作！");
            model.setSucceed(false);
            return model;
        } else if (UtilityEnum.SuperviseStatusEnum.TO_DO.getCode().equals(temp.getStatus())) {
            model.setErrMsg("该电子运单信息尚未保存，不能直接提交上报！");
            model.setSucceed(false);
            return model;
        } else if (UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode().equals(temp.getStatus())) {
            model.setErrMsg("该电子运单信息已上报成功，不能操作！");
            model.setSucceed(false);
            return model;
        } else if (UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode().equals(temp.getStatus())) {
            model.setErrMsg("该电子运单信息上报中，不能操作！");
            model.setSucceed(false);
            return model;
        }

        //保存运单信息
        saveWaybillBusInfo(waybill);
        BeanUtil.copyProperties(waybill, temp, CopyOptions.create().ignoreNullValue().ignoreError());

        ResultMode resultMode = SpvSpringFactory.getAopProxy(this).report(temp);
        if (resultMode.isSucceed()) {
            //手动上报成功，更新上报方式
            log.info("submitWaybill#运单【{}】手动上报成功", temp.getShippingNoteNumber());
            waybillBusinessRepository.updateAutoReportStatus(
                temp.getShippingNoteNumber(), SuperviseEnmus.AutoReportStatusEnum.THIRTY.getCode(), "");
        }
        return resultMode;
    }

    /**
     * 保存运单信息
     */
    private void saveWaybillBusInfo(Waybill waybill) {
        //设置修改时间
        waybill.setModifyDate(new Date());
        waybill.setModifyBy(JwtUtil.getInstance().getUserBaseIdByToken());
        waybill.getWaybillAttribute().setShippingNoteNumber(waybill.getShippingNoteNumber());
        //更新电子运单信息
        int update = updateWaybillInfo(waybill);
        if (update <= 0) {
            log.error("reportAgain#更新电子运单信息失败，waybill：{}", JSONUtil.toJsonStr(waybill));
            throw new SupeWlydException("更新电子运单信息失败");
        }
    }

    @Override
    public ResultMode<Waybill> viewWaybillBusDetail(Waybill waybill) throws Exception {
        log.info("进入 WaybillServiceImpl.viewWaybillBusDetail  方法中查询参数{}", JSON.toJSONString(waybill));
        Waybill waybillParam = new Waybill();
        waybillParam.setShippingNoteNumber(waybill.getShippingNoteNumber());
        Waybill obj = waybillBusinessRepository.viewWaybillBusInfo(waybillParam);
        log.info("进入 waybillBusinessRepository.viewWaybillBusInfo方法返回结果{}", JSON.toJSONString(obj));
        ResultMode<Waybill> mode = new ResultMode<>();
        if (null != obj) {
            if (obj.getTransportCosts() == null) {
                obj.setTransportCosts(new BigDecimal(0.00));
            }
            mode.getModel().add(obj);
        }
        return mode;
    }

    @Override
    public ResultMode<Waybill> viewWaybillReportDetail(Waybill waybill) throws Exception {
        log.info("进入 WaybillServiceImpl.viewWaybillReportDetail  方法中查询参数{}", JSON.toJSONString(waybill));
        Waybill waybillParam = new Waybill();
        waybillParam.setShippingNoteNumber(waybill.getShippingNoteNumber());
        Waybill obj = waybillBusinessRepository.viewWaybillBusInfo(waybillParam);
        if(obj==null){
            return ResultMode.success();
        }
        //风险等级
        String riskLevel = obj.getRiskLevel();
        log.info("WaybillServiceImpl#viewWaybillReportDetail 风险等级：{}", riskLevel);
        WaybillAttribute waybillAttribute = waybillAttributeBusinessRepository.queryWaybillAttributeByShippingNumber(obj.getShippingNoteNumber());
        obj.setWaybillAttribute(waybillAttribute);

        obj.setBusiType(RabbitConstants.BusiType.WLHY_WAYBILL);
        ResultMode resultMode = busiMsgService.queryMappingResults(obj);
        if(!resultMode.isSucceed()){
            return ResultMode.success();
        }
        obj = (Waybill)resultMode.getModel().get(0);
        if (null != obj) {
            //前端回显
            obj.setRiskLevel(riskLevel);

            //体积字段转换
            obj.getWaybillAttribute().setCube(obj.getWaybillAttribute().getGoodsCube());

            log.info("进入 waybillReportRepository.viewWaybillReportDetail方法返回结果{}", JSON.toJSONString(obj));
            if (obj.getTransportCosts() == null) {
                obj.setTransportCosts(new BigDecimal(0.000));
            }

        }
        ResultMode<Waybill> mode = new ResultMode<>();
        mode.getModel().add(obj);
        return mode;
    }

    @Override
    public ResultMode<String> batchReportWaybill(List<String> waybillIdList) throws Exception {
        ResultMode<String> resultModel = new ResultMode<>();

        if (waybillIdList == null || waybillIdList.size() == 0) {
            resultModel.setErrMsg("waybillIdList不能成功");
            resultModel.setSucceed(false);
        }

        WaybillSearchVo vo = new WaybillSearchVo();
        List<String> statusList = new ArrayList<String>();
        statusList.add(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        statusList.add(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode());
        vo.setIdList(waybillIdList);
        vo.setStatusList(statusList);
        List<String> searchResult = searchExistsData(vo);
        if (searchResult != null && searchResult.size() > 0) {///有数据代表着可以提交
            String msg = "";
            List<String> msgList = new ArrayList<>();
            for (String name : searchResult) {
                msgList.add(name);
            }
            msg = String.join(",", msgList);
            resultModel.setErrMsg(msg + " 电子运单信息已经处理，请勿重复处理");
            resultModel.setSucceed(false);
        } else {
            return checkAndReport(waybillIdList);
        }

        return resultModel;
    }

    @Override
    public void updateReportStatus(ReportMsgVO reportMsg) {
        Waybill waybill = new Waybill();
        waybill.setShippingNoteNumber(reportMsg.getBusiId());
        waybill.setSendToProDateTime(reportMsg.getReportTime());
        waybill.setStatus(reportMsg.getStatus());
        waybill.setModifyDate(new Date());
        waybill.setSuccessMessage(reportMsg.getResultInfo());

        //运单上报成功回调结算
        if (UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode().equals(reportMsg.getStatus())) {
            Map<String, String> map = Maps.newHashMap();
            map.put("waybillId", reportMsg.getBusiId());
            map.put("isReportSupervise", WaybillEnmu.IsReportSuperviseEnmu.ONE.getCode());
            kafkaUtil.kafkaProducerSend(KaFkaTopicNameEnmu.SUPERVISE_REPORT_STATUS_TO_BMS.getTopicName(), JSONUtil.toJsonStr(map));
        }

        int i = waybillBusinessRepository.updateReportStatusOfCompleted(waybill);
        if (i > 0) {
            //更新ES运单状态
            updateESWaybillStatus(waybill.getShippingNoteNumber(), waybill.getStatus(), null);
        }
    }

    @Override
    public Waybill queryWaybilByShippingNumber(String shippingNoteNumber) {
        if(StringUtils.isEmpty(shippingNoteNumber)){
            return null;
        }
        Waybill waybill = new Waybill();
        waybill.setShippingNoteNumber(shippingNoteNumber);
        return waybillBusinessRepository.viewWaybillBusInfo(waybill);
    }

    /**
     * 上报监管平台
     *
     * @param waybill
     * @return
     * @throws JAXBException
     */
    @Transactional(rollbackFor = {Exception.class})
    public ResultMode report(Waybill waybill) {
        //分布式锁
        String lockKey = SuperviseConstants.SUPERVISE_WAYBILL_REPORT_REDIS_KEY + waybill.getShippingNoteNumber();
        boolean flag = RedisUtil.setnx(lockKey, "1" , 10) > 0;
        log.info("电子运单上报#lockKey:{}, flag:{}", lockKey, flag);
        if (!flag) {
            return ResultMode.fail("运单[" + waybill.getShippingNoteNumber() + "上报，重复操作");
        }

        ResultMode resultMode;
        try {
            waybill.setBusiType(RabbitConstants.BusiType.WLHY_WAYBILL);
            waybill.setBusiId(waybill.getShippingNoteNumber());

            //查询明细
            WaybillAttribute waybillAttribute = waybillAttributeBusinessRepository.queryWaybillAttributeByShippingNumber(waybill.getShippingNoteNumber());
            if (waybillAttribute == null) {
                return ResultMode.fail("运单明细[" + waybill.getShippingNoteNumber() + "]没有找到");
            }
            waybill.setWaybillAttribute(waybillAttribute);
            //保险公司代码 为空转换
            if (StrUtil.isBlank(waybill.getWaybillAttribute().getInsuranceCompanyCode())) {
                waybill.getWaybillAttribute().setInsuranceCompanyCode("none");
            }
            //数据校验
            resultMode = dataCheck(waybill);
            if (!resultMode.isSucceed()) {
                return resultMode;
            }
            //检验司机和车辆是否上报
            resultMode = checkDriverAndVehicle(waybill);
            if (!resultMode.isSucceed()) {
                return resultMode;
            }
            if (!StringUtils.isEmpty(waybill.getNetworkMainBodyId())) {
                waybill.setNetworkMainBodyIdList(Arrays.asList(waybill.getNetworkMainBodyId()));
            }

            //设置网络货运主体
            resultMode = busiMsgService.setNetworkId(waybill);
            if (!resultMode.isSucceed()) {
                return resultMode;
            }

            //更新上报状态
            Waybill tempWaybill = new Waybill();
            tempWaybill.setShippingNoteNumber(waybill.getShippingNoteNumber());
            tempWaybill.setStatus(UtilityEnum.SuperviseStatusEnum.TO_REPORT.getCode());
            tempWaybill.setModifyDate(new Date());
            tempWaybill.setSuccessMessage("");
            String userBaseId = StrUtil.blankToDefault(JwtUtil.getInstance().getUserBaseIdByToken(), "");
            tempWaybill.setModifyBy(userBaseId);
            tempWaybill.setReporterId(userBaseId);
            int i = waybillBusinessRepository.updateReportingStatus(tempWaybill);
            if (i > 0) {
                //更新ES运单状态
                updateESWaybillStatus(waybill.getShippingNoteNumber(), tempWaybill.getStatus(), null);
            }

            resultMode = busiMsgService.convertAndSend(waybill);

        } catch (Exception e) {
            String errorMsg = "电子运单"+ waybill.getShippingNoteNumber() +"上报异常";
            log.error(errorMsg + "，异常信息：{}", e);
            return ResultMode.fail(errorMsg);
        } finally {
            log.info("电子运单{}上报完成，释放锁", waybill.getShippingNoteNumber());
            RedisUtil.del(lockKey);
        }

        return resultMode;
    }

    /**
     * 数据校验
     *
     * @param waybill
     * @return com.isoftstone.hig.common.model.ResultMode
     * <AUTHOR>
     * @Date 2023/8/4 17:45
     */
    private ResultMode dataCheck(Waybill waybill) {
        //运单生成时间
        String consignorDateStr = waybill.getConsignmentDateTime();
        //发货日期时间
        String despatchActualDateStr = waybill.getDespatchActualDateTime();
        //收货日期时间
        String goodsReceiptDateTimeStr = waybill.getGoodsReceiptDateTime();
        if (StrUtil.isBlank(despatchActualDateStr)) {
            return ResultMode.fail("发货日期时间为空");
        }
        if (StrUtil.isBlank(goodsReceiptDateTimeStr)) {
            return ResultMode.fail("收货日期时间为空");
        }

        DateTime consignorDate= null;
        DateTime despatchActualDate = null;
        DateTime goodsReceiptDate= null;
        //运单生成时间<发货日期时间<收货日期时间
        if (StrUtil.isNotBlank(consignorDateStr)) {
            consignorDate = DateUtil.parse(consignorDateStr, DatePattern.NORM_DATETIME_PATTERN);
            despatchActualDate = DateUtil.parse(despatchActualDateStr, DatePattern.NORM_DATETIME_PATTERN);
            goodsReceiptDate = DateUtil.parse(goodsReceiptDateTimeStr, DatePattern.NORM_DATETIME_PATTERN);
            if (DateUtil.compare(consignorDate, despatchActualDate) >= 0) {
                return ResultMode.fail("运单生成时间晚于发货日期时间");
            }
            if (DateUtil.compare(despatchActualDate, goodsReceiptDate) >= 0) {
                return ResultMode.fail("发货日期时间晚于收货日期时间");
            }

            //车辆注册日期校验
            Vehicle vehicle = new Vehicle();
            vehicle.setVehicleNumber(waybill.getWaybillAttribute().getVehicleNumber());
            vehicle.setVehiclePlateColorCode(waybill.getWaybillAttribute().getVehiclePlateColorCode());
            Vehicle veh = vehicleBusinessRepository.queryVehicleBusInfo(vehicle);
            if (ObjectUtil.isEmpty(veh)) {
                return ResultMode.fail("上报运单信息[" + waybill.getShippingNoteNumber() + "]，车辆[" + waybill.getWaybillAttribute().getVehicleNumber() + "]信息不存在");
            }
            String veConDate = veh.getRegisterDate();
            if (StrUtil.isNotBlank(veConDate) && consignorDate.before(DateUtil.parseDate(veConDate))) {
                return ResultMode.fail("上报运单信息[" + waybill.getShippingNoteNumber() + "]车辆注册时间不能晚于签订协议时间");
            }
        }

        //运输里程/（收货时间-发货时间）<=配置参数的车速上限
        List<PlatformCmPlatformParameter> platformParameter = platformExchangeService.getParameterByParaCodes(Arrays.asList("1165", "2007"));
        log.info("getByParaCodes#获取平台参数设置结果：{}", JSONUtil.toJsonStr(platformParameter));
        if (IterUtil.isEmpty(platformParameter)) {
            return ResultMode.fail("获取平台参数设置失败");
        }
        Map<String, PlatformCmPlatformParameter> paramMap = platformParameter.stream()
            .collect(Collectors.toMap(PlatformCmPlatformParameter::getParaCode, Function.identity()));

        PlatformCmPlatformParameter vehicleSpeed = paramMap.get("1165");
        if (vehicleSpeed == null) {
            return ResultMode.fail("获取平台车速上限参数失败");
        }
        String upperLimit = vehicleSpeed.getParaValue();
        //车速上限（km）
        double maxSpeed = 1;
        if (StrUtil.isNotBlank(upperLimit)) {
            maxSpeed = Double.parseDouble(upperLimit);
        }
        //运输里程（m）
        Integer transportMile = waybill.getTransportMileage();
        if (ObjectUtil.isEmpty(transportMile)) {
            return ResultMode.fail("运输里程为空");
        }

        PlatformCmPlatformParameter minDistanceParam = paramMap.get("2007");
        if (minDistanceParam == null) {
            return ResultMode.fail("获取平台线路最小距离参数失败");
        }
        String minDistanceStr = minDistanceParam.getParaValue();
        double minDistance = 0;
        if (StrUtil.isNotBlank(minDistanceStr)) {
            minDistance = Double.parseDouble(minDistanceStr);
        }
        double kilometre = (double) transportMile / 1000;
        if (kilometre <= minDistance) {
            return ResultMode.fail("运输里程必须大于线路最小距离" + minDistance + "公里");
        }
        double second = DateUtil.between(despatchActualDate, goodsReceiptDate, DateUnit.SECOND);
        double interval = transportMile / 1000 / (second / 60 / 60);
        if (NumberUtil.compare(interval, maxSpeed) > 0) {
            return ResultMode.fail("超过配置车速" + maxSpeed + "公里/小时");
        }

        //货物项毛重=派车重量时，需要判断 重量<核定载质量(吨)
        SuperviseChannelConfig config = channelConfigService.getConfigByCustId(waybill.getNetworkMainBodyId());
        /*if (ObjectUtil.isNotEmpty(config) && StrUtil.equals(config.getWeightType(), WaybillEnmu.WeightTypeEnum.TWO.getCode())) {
            String goodsItemGrossWeightStr = waybill.getWaybillAttribute().getGoodsItemGrossWeight();
            if (StrUtil.isBlank(goodsItemGrossWeightStr)) {
                return ResultMode.fail("货物项毛重为空");
            }
            String vehicleNumber = waybill.getWaybillAttribute().getVehicleNumber();
            if (StrUtil.isNotBlank(vehicleNumber)) {
                try {
                    ResultMode<Vehicle> resultMode = vehicleService.viewVehicleBusDetail(vehicleNumber, waybill.getWaybillAttribute().getVehiclePlateColorCode());
                    log.info("WaybillServiceImpl#dataCheck 获取车辆核定载质量，vehicleNumber：{}，result：{}",vehicleNumber, JSONUtil.toJsonStr(resultMode));
                    if (ObjectUtil.isNotEmpty(resultMode) && IterUtil.isNotEmpty(resultMode.getModel())) {
                        Vehicle vehicle = IterUtil.getFirst(resultMode.getModel());
                        if (ObjectUtil.isNotEmpty(vehicle) && StrUtil.isNotBlank(vehicle.getVehicleTonnage())) {
                            //核定载质量(吨)
                            BigDecimal vehicleTonnage = NumberUtil.toBigDecimal(vehicle.getVehicleTonnage()).multiply(new BigDecimal(1000));
                            //货物项毛重（KGM）
                            BigDecimal goodsItemGrossWeight = NumberUtil.toBigDecimal(goodsItemGrossWeightStr);
                            if (goodsItemGrossWeight.compareTo(vehicleTonnage) > 0) {
                                return ResultMode.fail("最大货物项毛重" + vehicleTonnage + "KGM");
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("获取车辆详情异常：{}", e);
                    return ResultMode.fail("获取车辆详情异常");
                }
            }
        }*/

        //内蒙监管，实际承运人道路运输经营许可证号长度在12和50之间限制
        if (ObjectUtil.isNotEmpty(config) && config.getCarrierLicenseVerify() == 1) {
            String actualCarrierBusinessLicense = waybill.getWaybillAttribute().getActualCarrierBusinessLicense();
            int length = StrUtil.length(actualCarrierBusinessLicense);
            if (length < 12 || length > 50) {
                return ResultMode.fail("实际承运人道路运输许可证号长度范围需在12-50之间");
            }
        }


        String consignorCountrySubdivisionCode = waybill.getConsignorCountrySubdivisionCode();
        String consigneeCountrySubdivisionCode = waybill.getConsigneeCountrySubdivisionCode();
        if (StrUtil.isBlank(consignorCountrySubdivisionCode) || StrUtil.isBlank(consigneeCountrySubdivisionCode)) {
            return ResultMode.fail("装货地点/卸货地点不允许为空");
        }
        if (ObjectUtil.isNotEmpty(config) && StrUtil.isNotBlank(config.getPlatformCode())) {
            String platformCode = config.getPlatformCode();
            //校验装货地址城市是否匹配映射关系
            String subCode = checkSubCode(consignorCountrySubdivisionCode, platformCode);
            if (StrUtil.isNotBlank(subCode)) {
                return ResultMode.fail("装货地点城市编码"+ subCode + "无匹配映射结果");
            }
            //校验收货地址城市是否匹配映射关系
            subCode = checkSubCode(consigneeCountrySubdivisionCode, platformCode);
            if (StrUtil.isNotBlank(subCode)) {
                return ResultMode.fail("收货地点城市编码"+ subCode + "无匹配映射结果");
            }
        }

        return ResultMode.success();
    }

    private String checkSubCode(String subCode, String platformCode){
        String arr[] = subCode.split("-");
        for (String code : arr) {
            EnumMapping enumMapping = enumMappingService.queryEnumMappingBySystemEnumKey(platformCode, "district", code);
            if (ObjectUtil.isEmpty(enumMapping)) {
                return code;
            }
        }
        return null;
    }

    private ResultMode checkDriverAndVehicle(Waybill waybill){
        String drivingLicense = waybill.getWaybillAttribute().getDrivingLicense();

        //校验司机是否已经上传
        int driverCount = messageNoteService.checkIsDriverReport(drivingLicense,waybill.getNetworkMainBodyId());

        Vehicle vehicleVo = null;
        int vehicleCount = 0;
        String vehicleNumber = waybill.getWaybillAttribute().getVehicleNumber();
        String colorCode = waybill.getWaybillAttribute().getVehiclePlateColorCode();
        Vehicle searchVo = new Vehicle();
        searchVo.setVehicleNumber(vehicleNumber);
        searchVo.setVehiclePlateColorCode(colorCode);
        List<Vehicle> vehicleList = vehicleService.queryVehicle(searchVo);
        if (ObjectUtil.isNotEmpty(vehicleList)) {
            vehicleVo = IterUtil.getFirst(vehicleList);
            vehicleCount = messageNoteService.checkIsUploadData(String.valueOf(vehicleVo.getId()),RabbitConstants.BusiType.WLHY_VEHICLE,waybill.getNetworkMainBodyId());
        }
        //校验挂车是否已经上传（内蒙监管、车辆是牵引车是才检查）
        int trailerCount = 0;
        boolean needCheckTrailer = false;
        String trailerNumber = "";
        //查询渠道配置，是否开启挂车上报
        SuperviseChannelConfig config = channelConfigService.getConfigByCustId(waybill.getNetworkMainBodyId());
        if (ObjectUtil.isNotEmpty(config) && config.getReportTrailer() == 1) {
            //车辆类型为牵引车
            if (ObjectUtil.isNotEmpty(vehicleVo) && StrUtil.startWith(vehicleVo.getVehicleType(), "Q")) {
                needCheckTrailer = true;
                log.info("上报运单信息，运单挂车车牌号：{}", waybill.getWaybillAttribute().getTrailerVehiclePlateNumber());
                if (StrUtil.isNotBlank(waybill.getWaybillAttribute().getTrailerVehiclePlateNumber())) {
                    trailerNumber = waybill.getWaybillAttribute().getTrailerVehiclePlateNumber();
                    log.info("上报运单信息，运单挂车车牌号存在：{}", trailerNumber);
                    String trailerColor = waybill.getWaybillAttribute().getTrailerVehiclePlateColorCode();
                    Vehicle trailerFilter = new Vehicle();
                    trailerFilter.setVehicleNumber(trailerNumber);
                    trailerFilter.setVehiclePlateColorCode(trailerColor);
                    Vehicle trailer = vehicleBusinessRepository.queryVehicleBusInfo(trailerFilter);
                    if (trailer != null) {
                        trailerCount = messageNoteService.checkIsUploadData(trailer.getId(),RabbitConstants.BusiType.WLHY_VEHICLE,waybill.getNetworkMainBodyId());
                    }
                }
//                else if (StrUtil.isNotBlank(vehicle.getTrailerVehiclePlateNumber())) {
//                    trailerNumber = vehicle.getTrailerVehiclePlateNumber();
//                    trailerCount = messageNoteService.checkIsUploadData(trailerNumber,RabbitConstants.BusiType.WLHY_VEHICLE,waybill.getNetworkMainBodyId());
//                }
            }
        }

        StringBuffer stringBuffer = new StringBuffer();
        if (driverCount <= 0 || vehicleCount <= 0 || (needCheckTrailer && trailerCount <= 0)) {
            stringBuffer.append("运单号为" + waybill.getShippingNoteNumber() + "的");
            if (driverCount <= 0) {
                stringBuffer.append("司机");
                stringBuffer.append(waybill.getWaybillAttribute().getDriverName());
            }
            if (vehicleCount <= 0) {
                if (driverCount <= 0) {
                    stringBuffer.append("，");
                }
                stringBuffer.append("车牌号");
                stringBuffer.append(waybill.getWaybillAttribute().getVehicleNumber());
            }
            if (needCheckTrailer && trailerCount <= 0) {
                if (driverCount <= 0 || vehicleCount <= 0) {
                    stringBuffer.append("，");
                }
                stringBuffer.append("挂车车牌号");
                stringBuffer.append(StrUtil.blankToDefault(trailerNumber, ""));
                log.info("上报运单信息，挂车车牌号未上报：{}，{}", trailerNumber, StrUtil.blankToDefault(trailerNumber, ""));
            }
            stringBuffer.append("未上报");
            return ResultMode.fail(stringBuffer.toString());
        }

        return ResultMode.success();
    }

    /**
     * 校验并上报
     *
     * @param waybillIdList
     * @return
     * @throws JAXBException
     */
    public ResultMode<String> checkAndReport(List<String> waybillIdList){
        log.info("进入校验与上报逻辑"+JSONUtil.toJsonStr(waybillIdList));
        ResultMode<String> resultModel = new ResultMode<>();
        StringBuffer stringBuffer = new StringBuffer();
        for (String waybillId : waybillIdList) {
            Waybill temp = new Waybill();
            temp.setShippingNoteNumber(waybillId);
            Waybill waybill = waybillBusinessRepository.viewWaybillBusInfo(temp);
            if(waybill==null){
                return ResultMode.fail("上报运单信息["+temp.getShippingNoteNumber()+"]没有找到");
            }

            ResultMode tempResult  = SpvSpringFactory.getAopProxy(this).report(waybill);
            if (!tempResult.isSucceed()) {
                stringBuffer.append(tempResult.getErrMsg());
                stringBuffer.append(",");
            }
            if (tempResult.isSucceed()) {
                //手动上报成功，更新上报方式
                log.info("checkAndReport#运单【{}】手动上报成功", waybillId);
                waybillBusinessRepository.updateAutoReportStatus(
                    waybillId, SuperviseEnmus.AutoReportStatusEnum.THIRTY.getCode(), "");
            }
        }
        if (StringUtils.isNotBlank(stringBuffer.toString())) {
            resultModel.setSucceed(true);
            resultModel.getModel().add(stringBuffer.substring(0, stringBuffer.length() - 1));
        }
        return resultModel;
    }

    /**
     * 转换数据
     *
     * @param waybill
     */
    private void formatWeight(Waybill waybill) {
        DecimalFormat df = new DecimalFormat("0.000");
        if (StringUtils.isNotBlank(waybill.getTotalMonetaryAmount())) {
            waybill.setTotalMonetaryAmount(df.format(new BigDecimal(waybill.getTotalMonetaryAmount())));
        }

        WaybillAttribute attribute = waybill.getWaybillAttribute();
        if (StringUtils.isNotBlank(attribute.getTotalNumberOfPackages())) {
            attribute.setTotalNumberOfPackages(attribute.getTotalNumberOfPackages() == null ? "0" : attribute.getTotalNumberOfPackages());
        }
        if (StringUtils.isNotBlank(attribute.getGoodsItemGrossWeight())) {
            attribute.setGoodsItemGrossWeight(df.format(new BigDecimal(attribute.getGoodsItemGrossWeight())
                    .setScale(3, BigDecimal.ROUND_HALF_UP)));
        }
        if (StringUtils.isNoneBlank(attribute.getCube())) {
            df = new DecimalFormat("0.0000");
            attribute.setCube(df.format(new BigDecimal(attribute.getCube() == null ? "0" : attribute.getCube())
                    .setScale(4, BigDecimal.ROUND_HALF_UP)));
        }

    }

    /**
     * searchExistsData:        检验是否重复操作
     *
     * @param vo
     * @return
     */
    private List<String> searchExistsData(WaybillSearchVo vo) {
        log.info("进入 DriverServiceImpl.searchExistsData  方法中查询参数{}", JSON.toJSONString(vo));
        List<String> result = waybillBusinessRepository.searchExistsData(vo);
        if (result != null && result.size() > 0) {
            return result;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Boolean updateWaybillBusInfoStatusByWaybillId(String waybillId) {
        log.info("WaybillServiceImpl#updateWaybillBusInfoStatusByWwaybillId入参为：{}", WlydGsonUtils.convertObjToJSONStr(waybillId));
        Waybill waybill = new Waybill();
        waybill.setShippingNoteNumber(waybillId);
        waybill.setStatus(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        waybill.setModifyDate(new Date());
        int i = waybillBusinessRepository.updateReportingStatus(waybill);
        if (i > 0) {
            //更新ES运单状态
            updateESWaybillStatus(waybillId, waybill.getStatus(), null);
        }
        return i > 0;
    }

    @Override
    public List<Waybill> queryReportWaybill(Integer start, Integer end, Map<String, Object> paramMap) {
        List<String> statusList = (List<String>)paramMap.get("status");
        String networkMainBodyId = (String)paramMap.get("networkMainBodyId");
        return waybillBusinessRepository.queryReportWaybillData(start,end,statusList,networkMainBodyId);
    }

    @Override
    @Transactional
    public void updateTerminationWaybillAmountByWaybillId(String waybillId, BigDecimal amount) {
        if (StrUtil.isBlank(waybillId) || ObjUtil.isNull(amount)) {
            log.error("updateTerminationWaybillAmountByWaybillId#参数不允许为空");
            throw new SupeWlydException("参数不允许为空");
        }
        log.info("WaybillServiceImpl#updateTerminationWaybillAmountByWaybillId运单号：{},金额：{}", waybillId, amount);
        int a = waybillBusinessRepository.updateTerminationWaybillAmountByWaybillId(waybillId, amount);
        log.info("WaybillServiceImpl#updateTerminationWaybillAmountByWaybillId更新结果a：{}", a);

        int b = waybillBusinessRepository.updateTerminationWaybillInfoAmountExByWaybillId(waybillId, amount);
        log.info("WaybillServiceImpl#updateTerminationWaybillAmountByWaybillId更新结果b：{}", b);
        if (b > 0) {
            int c = waybillBusinessRepository.updateTerminationWaybillAttrAmountExByWaybillId(waybillId, amount);
            log.info("WaybillServiceImpl#updateTerminationWaybillAmountByWaybillId更新结果c：{}", c);
        }
    }

    /**
     * 更新运单信息
     *
     * @param waybill
     * @return void
     * <AUTHOR>
     * @Date 2023/9/5 18:57
     */
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public void updateWaybill(Waybill waybill) {
        if (StrUtil.isNotBlank(waybill.getShippingNoteNumber())) {
            Waybill oldWaybill = queryWaybilByShippingNumber(waybill.getShippingNoteNumber());
            if (ObjectUtil.isNotEmpty(oldWaybill)) {
                waybill.setModifyDate(Optional.ofNullable(waybill.getModifyDate()).orElse(DateUtil.date()));
                waybillBusinessRepository.update(waybill);

                //更新es监管运单状态
                updateESWaybillStatus(waybill.getShippingNoteNumber(), waybill.getStatus(), oldWaybill.getStatus());
            }
        }

        WaybillAttribute waybillAttribute = waybill.getWaybillAttribute();
        if (ObjectUtil.isNotEmpty(waybillAttribute) && StrUtil.isNotBlank(waybillAttribute.getShippingNoteNumber())) {
            //处理实际承运人名称
            String actualCarrierName = waybillAttribute.getActualCarrierName();
            if (StrUtil.isNotBlank(actualCarrierName)) {
                //历史运单信息
                Waybill waybillInfoOld = queryWaybillInfoByWaybillId(waybillAttribute.getShippingNoteNumber());
                if (waybillInfoOld != null && waybillInfoOld.getWaybillAttribute() != null) {
                    //设置为新收款人名称
                    waybillInfoOld.getWaybillAttribute().setActualCarrierName(actualCarrierName);
                    //处理实际承运人名称，改成格式为：车辆所有人（实际收款人）
                    handleCarrier(waybillInfoOld);
                    //替换实际承运人名称承运人
                    waybillAttribute.setActualCarrierName(waybillInfoOld.getWaybillAttribute().getActualCarrierName());
                }
            }
            waybillAttributeBusinessRepository.update(waybillAttribute);
        }
    }

    /**
     * 查 waybill info+attribute
     * @param waybillId
     * @return
     */
    private Waybill queryWaybillInfoByWaybillId(String waybillId) {
        if (StrUtil.isBlank(waybillId)) {
            return null;
        }
        return waybillBusinessRepository.queryWaybillInfoByWaybillId(waybillId);
    }

    /**
     * 同步监管运单信息到 ES
     *
     * @param waybill
     * @return void
     * <AUTHOR>
     * @Date 2023/9/12 17:09
     */
    @Override
    public void synWaybillToEs(Waybill waybill) {
        log.info("synWaybillToEs 入参：{}", JSONUtil.toJsonStr(waybill));
        //supervise_waybill_bus_info
        List<DcsWaybillDto> waybillInfoList = waybillBusinessRepository.searchWaybillBusInfoByCondition(waybill);
        saveESWaybillStatusBatch(waybillInfoList);

        //supervise_waybill_xbus_info
        List<DcsWaybillDto> waybillXBusInfoList = waybillBusinessRepository.searchWaybillXBusInfoByCondition(waybill);
        saveESWaybillStatusBatch(waybillXBusInfoList);
    }

    /**
     * 更新es监管运单状态
     *
     * @param waybillId 运单号
     * @param status    新运单状态
     * @param oldStatus 历史运单状态
     * @return void
     * <AUTHOR>
     * @Date 2023/9/11 9:54
     */
    private void updateESWaybillStatus(String waybillId, String status, String oldStatus) {
        if (StrUtil.isBlank(waybillId) || StrUtil.isBlank(status)) {
            return;
        }

        if (!StrUtil.equals(status, oldStatus)) {
            DcsWaybillDto dcsWaybillDto = new DcsWaybillDto();
            dcsWaybillDto.setId(waybillId);
            dcsWaybillDto.setWaybillId(waybillId);
            dcsWaybillDto.setWaybillReportStatus(status);
            try {
                String result = dcsWaybillService.save(dcsWaybillDto);
                log.info("更新es监管运单状态：{}，result：{}", JSONUtil.toJsonStr(dcsWaybillDto), result);
            } catch (Exception e) {
                log.error("更新es监管运单状态失败：{}，errMsg：{}", JSONUtil.toJsonStr(dcsWaybillDto), e);
            }
        }
    }

    /**
     * 批量存储es监管运单状态
     *
     * @param dcsWaybillDtoList
     * @return void
     * <AUTHOR>
     * @Date 2023/9/12 10:34
     */
    private void saveESWaybillStatusBatch(List<DcsWaybillDto> dcsWaybillDtoList) {
        if (IterUtil.isEmpty(dcsWaybillDtoList)) {
            return;
        }

        try {
            String result = dcsWaybillService.saveList(dcsWaybillDtoList);
            log.info("批量存储es监管运单状态，result：{}", result);
        } catch (Exception e) {
            log.error("批量存储es监管运单状态失败：{}，errMsg：{}", JSONUtil.toJsonStr(dcsWaybillDtoList), e);
        }
    }

    /**
     * 根据运单到获取运单属性
     * @param waybillId
     * @return
     */
    @Override
    public WaybillAttribute getWaybillAttributeByWaybillId(String waybillId) {
        if (StrUtil.isBlank(waybillId)) {
            return null;
        }
        return waybillAttributeBusinessRepository.queryWaybillAttributeByShippingNumber(waybillId);
    }

    /**
     * 自动上报电子运单查询
     * @param length  上报条数
     * @param waybillDto 查询条件
     */
    private List<Waybill> queryAutoReportWaybill(Integer length, WaybillReportDto waybillDto) {
        if (length > 0) {
            PageHelper.startPage(0, length, false);
        }
        return waybillBusinessRepository.queryAutoReportWaybill(waybillDto);
    }

    /**
     * 电子运单自动上报 网络货运主体+运单未自动上报过的+运单状态已保存+运单低风险+最近一个月内最早的50条
     * @param custIds 网络货运主体
     * @param map  上报条件
     */
    @Override
    public void autoReportWaybill(List<String> custIds, Map<String, Object> map) {
        Integer length = Integer.parseInt(map.get("length").toString());
        Integer month = Integer.parseInt(map.get("month").toString());
        Integer delayMinute = Integer.parseInt(map.get("delayMinute").toString());

        custIds.stream().forEach(custId -> {
            if (StrUtil.isBlank(custId)) {
                return;
            }
            SuperviseChannelConfig config = channelConfigService.getConfigByCustId(custId);

            WaybillReportDto waybillDto = new WaybillReportDto();
            waybillDto.setNetworkMainBodyId(custId);
            waybillDto.setAutoReportStatus(SuperviseEnmus.AutoReportStatusEnum.TEN.getCode());
            waybillDto.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
            waybillDto.setRiskLevel("200");
            waybillDto.setCreateDate(DateUtil.offsetMonth(DateUtil.beginOfDay(DateUtil.date()), month));
            waybillDto.setCapitalVerify(config.getCapitalVerify());
            List<Waybill> waybillList = queryAutoReportWaybill(length, waybillDto);
            if (IterUtil.isEmpty(waybillList)) {
                log.info("autoReportWaybill#运单自动上报，无满足条件的运单");
                return;
            }

            //分批处理
            int batchSize = Integer.parseInt(map.get("batchSize").toString());
            IntStream.range(0, waybillList.size()).boxed()
                .collect(Collectors.groupingBy(index -> index / batchSize))
                .forEach((batchIndex, batch) -> {
                    List<Waybill> subList = waybillList.subList(batch.get(0), batch.get(batch.size() - 1) + 1);
                    batchHandelAutoReportWaybill(subList, custId,delayMinute);
                });

        });
    }

    /**
     * 分批处理自动上报运单
     */
    private void batchHandelAutoReportWaybill(List<Waybill> waybillList, String custId, Integer delayMinute) {
        ReportDTO reportDTO = differentiateWaybillProcessing(waybillList, custId);
        if(reportDTO==null){
            log.info("autoReportWaybill#查询运单明细为空");
            return;
        }

        //上报车辆
        vehicleService.reportVehicleList(reportDTO.getReportVehicleList());

        //上报司机
        driverService.reportDriverList(reportDTO.getReportDrivingList());

        List<Waybill> reportWaybillList = reportDTO.getReportWaybillList();
        if (IterUtil.isEmpty(reportWaybillList)){
            return;
        }

        //上报运单
        reportWaybillList.forEach(waybill -> {
            if(!checkWaybillDelayTime(waybill,delayMinute)){
                return;
            }
            handelAutoReportWaybill(waybill);
        });
    }

    private void handelAutoReportWaybill(Waybill waybill) {
        //校验资金流水
        /*if (!capitalVerify(waybill)) {
            if (StrUtil.startWith(waybill.getSuccessMessage(), SupeStatusCodeEnum.BUSS_SUCCESS_BSUP0500.getCode())) {
                log.info("autoReportWaybill#运单【{}】未产生资金流水，不更新message", waybill.getShippingNoteNumber());
                return;
            }
            String errorMsg = StrUtil.format(SupeStatusCodeEnum.BUSS_SUCCESS_BSUP0500.getMsg(), waybill.getShippingNoteNumber());
            log.info("autoReportWaybill#" + errorMsg);
            StringBuffer stringBuffer = new StringBuffer()
                .append(SupeStatusCodeEnum.BUSS_SUCCESS_BSUP0500.getCode())
                .append(":")
                .append(errorMsg);
            //异常更新异常信息
            waybillBusinessRepository.updateAutoReportStatus(waybill.getShippingNoteNumber(), null, stringBuffer.toString());
            return;
        }*/

        //获取配置中需要修改运输完成时间的货运主体id
        List<String> modifyTransportEndDateCustIds = channelConfigService.getModifyTransportEndDateCustIds();
        if (IterUtil.isNotEmpty(modifyTransportEndDateCustIds) && modifyTransportEndDateCustIds.contains(waybill.getNetworkMainBodyId())) {
            Date payDate = null;
            CapitalAccount filter = new CapitalAccount();
            filter.setShippingNoteNumber(waybill.getShippingNoteNumber());
            ResultMode<CapitalAccount> resultMode = capitalAccountService.getCapitalAccountDetail(filter);
            if (IterUtil.isNotEmpty(resultMode.getModel())) {
                CapitalAccount capitalAccount = IterUtil.getFirst(resultMode.getModel());
                payDate = DateUtil.parseDateTime(capitalAccount.getDateTime());
            } else {
                //查询toB资金
                List<SuperviseCapitalFlowPayment> flowPayments = superviseCapitalFlowPaymentMapper.selectByShippingNoteNumber(waybill.getShippingNoteNumber());
                if (IterUtil.isNotEmpty(flowPayments)) {
                    payDate = flowPayments.stream().map(SuperviseCapitalFlowPayment::getDateTime).max(Date::compareTo).get();
                }
            }

            if (payDate == null) {
                log.info("autoReportWaybill#运单【{}】自动上报发生错误，未查询到资金支付时间", waybill.getShippingNoteNumber());
                return;
            }

            Date transportEndDate = waybill.getTransportEndDate();
            log.info("autoReportWaybill#修改前：运输完结时间:{},支付时间：{}", transportEndDate, payDate);
            //运输完结时间 取随机值
            Date randomTransportEndDate = SpvDateUtil.randomTransportEndDate(transportEndDate, payDate);
            if (ObjUtil.isNotNull(randomTransportEndDate)) {
                waybill.setTransportEndDate(randomTransportEndDate);
                log.info("autoReportWaybill#修改后：运输完结时间:{},支付时间：{}", randomTransportEndDate, payDate);
                waybillBusinessRepository.updateTransportEndDate(waybill);
            }
        }

        String waybillId = waybill.getShippingNoteNumber();
        //判断运输完成-当前上报时间>24，如果大于，更新上报状态为失败，原因为"运单完结时间超过上报时间24小时"
        SuperviseChannelConfig config = channelConfigService.getConfigByCustId(waybill.getNetworkMainBodyId());
        if (config.getTimeCheckFlag() == 1) {
            if (SpvDateUtil.checkDateInterval(waybill.getTransportEndDate(), DateUtil.date(), SuperviseConstants.TIME_INTERVAL_TRANSPORT_PAY)) {
                String errMsg = StrUtil.format(SupeStatusCodeEnum.BUSS_SUCCESS_BSUP0501.getMsg(), SuperviseConstants.TIME_INTERVAL_TRANSPORT_PAY);
                log.info("autoReportWaybill#运单【{}】自动上报发生错误，错误信息：{}", waybillId, errMsg);
                //异常更新异常信息
                Waybill updWaybill = new Waybill();
                updWaybill.setShippingNoteNumber(waybillId);
                updWaybill.setStatus(UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode());
                updWaybill.setSuccessMessage(errMsg);
                updWaybill.setModifyDate(DateUtil.date());
                waybillBusinessRepository.updateReportingStatus(updWaybill);
                return;
            }
        }

        ResultMode resultMode = SpvSpringFactory.getAopProxy(this).report(waybill);
        if (!resultMode.getSucceed()) {
            log.info("autoReportWaybill#运单【{}】自动上报发生错误，错误信息：{}", waybillId, resultMode.getErrMsg());
            //异常更新异常信息
            waybillBusinessRepository.updateAutoReportStatus(waybillId, null, resultMode.getErrMsg());
            return;
        }

        //成功更新上报方式为自动上报
        log.info("autoReportWaybill#运单【{}】自动上报成功", waybillId);
        waybillBusinessRepository.updateAutoReportStatus(waybillId, SuperviseEnmus.AutoReportStatusEnum.TWENTY.getCode(), null);
    }

    /**
     * 区分出需要上报的运单和需要上报司机/车辆的运单集合
     * @param waybillList 运单集合
     * @param custId 网络货运主体id
     */
    private ReportDTO differentiateWaybillProcessing(List<Waybill> waybillList,String custId) {
        ReportDTO reportDTO = new ReportDTO();
        //获取所有车辆/司机上报的网络主体，用于判断是否上报车辆/司机
        List<String> vehicleDriverReportCustIds = channelConfigService.getAutoVehicleDriverReportCustIds();
        if (IterUtil.isEmpty(vehicleDriverReportCustIds) || !vehicleDriverReportCustIds.contains(custId)) {
            reportDTO.setReportWaybillList(waybillList);
            return reportDTO;
        }

        //获取所有上报的网络主体，用于判断是否上报挂车
        boolean isTrailer = channelConfigService.getTrailerReportFlag(custId);
        List<String> shippingNoteNumberList = waybillList.stream().map(Waybill::getShippingNoteNumber).collect(Collectors.toList());
        List<WaybillAttribute> waybillAttributes = waybillAttributeBusinessRepository.queryWaybillAttributeByShippingNumberList(shippingNoteNumberList);
        if (IterUtil.isEmpty(waybillAttributes)){
            log.info("电子运单自动上报获取运单详情失败,运单号:{}", String.join(",", shippingNoteNumberList));
            return null;
        }
        //获取需要上报的车辆信息
        Map<String,Vehicle> reportVehicleMap = getReportVehicleList(waybillAttributes, custId, isTrailer);

        //获取需要上报的司机信息
        Map<String,Driver> reportDriverMap = getReportDriverList(waybillAttributes, custId);

        //区分需要上报的运单以及上报的车辆和司机
        Map<String, Waybill> waybillMap = waybillList.stream().collect(Collectors.toMap(Waybill::getShippingNoteNumber, Function.identity(), (key1, key2) -> key2));
        List<Waybill> reportWaybillList = new ArrayList<>();
        waybillAttributes.forEach(waybillAttribute -> {
            //车辆未上报
            String vehicleInfo = waybillAttribute.getVehicleNumber()+"_"+waybillAttribute.getVehiclePlateColorCode();
            if (reportVehicleMap.containsKey(vehicleInfo)){
                return;
            }
            //司机未上报
            if (reportDriverMap.containsKey(waybillAttribute.getDrivingLicense())){
                return;
            }
            //挂车未上报
            if (isTrailer && StrUtil.isNotBlank(waybillAttribute.getTrailerVehiclePlateNumber()) && StrUtil.isNotBlank(waybillAttribute.getTrailerVehiclePlateNumber())) {
                String trailerInfo = waybillAttribute.getTrailerVehiclePlateNumber()+"_"+waybillAttribute.getTrailerVehiclePlateColorCode();
                if (reportVehicleMap.containsKey(trailerInfo)){
                    return;
                }
            }
            Waybill waybill = waybillMap.get(waybillAttribute.getShippingNoteNumber());
            reportWaybillList.add(waybill);
        });
        reportDTO.setReportWaybillList(reportWaybillList);
        reportDTO.setReportVehicleList(new ArrayList<>(reportVehicleMap.values()));
        reportDTO.setReportDrivingList(new ArrayList<>(reportDriverMap.values()));
        return reportDTO;
    }

    /**
     * 获取需要上报的车辆信息
     * @param waybillAttributeList
     * @return
     */
    private Map<String,Vehicle> getReportVehicleList(List<WaybillAttribute> waybillAttributeList, String custId, boolean isTrailer) {

        Map<String,Vehicle> vehicleMap =  new HashMap<>();
        Map<String, Vehicle> finalVehicleMap = vehicleMap;
        waybillAttributeList.forEach(waybillAttribute -> {
            String uniqueId=waybillAttribute.getVehicleNumber()+"_"+waybillAttribute.getVehiclePlateColorCode();

            //添加车辆
            if(!finalVehicleMap.containsKey(uniqueId)){
                Vehicle vehicle = new Vehicle();
                vehicle.setVehicleNumber(waybillAttribute.getVehicleNumber());
                vehicle.setVehiclePlateColorCode(waybillAttribute.getVehiclePlateColorCode());
                vehicle.setNetworkMainBodyId(custId);
                finalVehicleMap.put(uniqueId,vehicle);
            }

            //添加挂车
            if (isTrailer && StrUtil.isNotBlank(waybillAttribute.getTrailerVehiclePlateNumber()) && StrUtil.isNotBlank(waybillAttribute.getTrailerVehiclePlateColorCode())) {
                uniqueId=waybillAttribute.getTrailerVehiclePlateNumber()+"_"+waybillAttribute.getTrailerVehiclePlateColorCode();
                if(!finalVehicleMap.containsKey(uniqueId)){
                    Vehicle trailerVehicle = new Vehicle();
                    trailerVehicle.setVehicleNumber(waybillAttribute.getTrailerVehiclePlateNumber());
                    trailerVehicle.setVehiclePlateColorCode(waybillAttribute.getTrailerVehiclePlateColorCode());
                    trailerVehicle.setNetworkMainBodyId(custId);
                    finalVehicleMap.put(uniqueId,trailerVehicle);
                }
            }
        });

        List<String> vehicleNumberAllList = vehicleMap.values().stream().map(Vehicle::getVehicleNumber).collect(Collectors.toList());
        //根据车辆号查询已上报和上报中的车辆信息
        List<Vehicle> vehicleList = vehicleBusinessRepository.queryVehicleReport(vehicleNumberAllList, custId);
        if(IterUtil.isNotEmpty(vehicleList)){
            //已上报和上报中的车辆信息
            Map<String,Vehicle> reportVehicleMap = vehicleList.stream().collect(Collectors.toMap(item -> item.getVehicleNumber() + "_" + item.getVehiclePlateColorCode(), Function.identity(), (key1, key2) -> key2));

            //移除已上报和上报中的记录
            vehicleMap.keySet().removeAll(reportVehicleMap.keySet());
        }
        if (MapUtil.isEmpty(vehicleMap)){
            return Collections.emptyMap();
        }
        //返回需要上报的车辆信息
        Map<String, Vehicle> reportVehicleMap = getReportVehicleMap(vehicleMap, custId);
        return reportVehicleMap;
    }

    /**
     *  获取需要上报的车辆信息
     * @param vehicleMap 运单中需要上报的车辆信息（已排除已上报的车辆信息）
     * @param custId
     * @return
     */
    private Map<String, Vehicle> getReportVehicleMap(Map<String, Vehicle> vehicleMap, String custId) {
        List<String> unReportVehicleList = vehicleMap.values().stream().map(Vehicle::getVehicleNumber).collect(Collectors.toList());
        //根据车牌号查出相关所有车辆信息（包括同车牌异颜色）
        List<Vehicle> vehicles = vehicleBusinessRepository.queryVehicleByNumberList(unReportVehicleList);
        if(IterUtil.isEmpty(vehicles)){
            return Collections.emptyMap();
        }

        Map<String, Vehicle> reportVehicleMap = new HashMap<>();
        String key = "";
        for(Vehicle vehicle : vehicles){
            vehicle.setNetworkMainBodyId(custId);
            vehicle.setNetworkMainBodyIdList(Collections.singletonList(custId));

            key=vehicle.getVehicleNumber()+"_"+vehicle.getVehiclePlateColorCode();
            if(vehicleMap.containsKey(key)){
                reportVehicleMap.put(key,vehicle);
            }
        }
        return reportVehicleMap;
    }

    /**
     * 获取需要上报的司机证件信息
     * @param waybillAttributeList
     * @param custId
     * @return
     */
    private Map<String,Driver> getReportDriverList(List<WaybillAttribute> waybillAttributeList, String custId) {
        Set<String> drivingLicenseAllList = waybillAttributeList.stream().map(WaybillAttribute::getDrivingLicense).
            collect(Collectors.toSet());
        //根据司机证件号查询已上报和上报中的司机证件信息
        List<Driver> reportDrivingList = driverBusinessRepository.queryDriverReport(new ArrayList<>(drivingLicenseAllList), custId);
        if(IterUtil.isNotEmpty(reportDrivingList)){
            Set<String> reportDrivingLicenseSet = reportDrivingList.stream().map(Driver::getDrivingLicense).collect(Collectors.toSet());
            //移除已上报和上报中的记录
            drivingLicenseAllList.removeAll(reportDrivingLicenseSet);
        }
        if (IterUtil.isEmpty(drivingLicenseAllList)){
            return Collections.emptyMap();
        }
        List<Driver> driverList = driverBusinessRepository.queryDriverByDrivingLicense(new ArrayList<>(drivingLicenseAllList));
        if(IterUtil.isEmpty(driverList)){
            log.info("运单上报时资金校验，查询不到司机证件信息.证件集合:{}", JSON.toJSONString(drivingLicenseAllList));
            return Collections.emptyMap();
        }
        driverList.stream().filter(item -> {
            item.setNetworkMainBodyId(custId);
            item.setNetworkMainBodyIdList(Collections.singletonList(custId));
            return true;
        }).collect(Collectors.toList());
        return driverList.stream().collect(Collectors.toMap(Driver::getDrivingLicense, Function.identity(), (key1, key2) -> key2));
    }


    /**
     * 运单上报时资金校验
     */
    private boolean capitalVerify(Waybill waybill) {
        //资金流水校验开关
        SuperviseChannelConfig config = channelConfigService.getConfigByCustId(waybill.getNetworkMainBodyId());
        if (config == null) {
            log.info("capitalVerify#未查询到配置信息：{}", waybill.getNetworkMainBodyId());
            return false;
        }

        if (config.getCapitalVerify() == null || config.getCapitalVerify() == 0) {
            log.info("capitalVerify#资金流水校验开关未开启，不校验。运单：{}, 网络货运主体：{}", waybill.getShippingNoteNumber(), waybill.getNetworkMainBodyId());
            return true;
        }

        if (config.getCapitalVerify() == 1) {
//            CapitalAccount capitalAccount = new CapitalAccount();
//            capitalAccount.setShippingNoteNumber(waybill.getShippingNoteNumber());
//            ResultMode<CapitalAccount> resultMode = capitalAccountService.getCapitalAccountDetail(capitalAccount);
//            if (resultMode.getSucceed() && IterUtil.isNotEmpty(resultMode.getModel())) {
            if (StrUtil.equals(waybill.getPayStatus(), SuperviseEnmus.PayStatusEnum.TWENTY.getCode())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 电子运单信息重新上报-保存并上报
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultMode reportAgain(Waybill waybill) {
        //参数校验
        if (StrUtil.isBlank(waybill.getShippingNoteNumber())) {
            return ResultMode.fail("电子运单号不允许为空");
        }

        Waybill temp = waybillBusinessRepository.viewWaybillBusInfo(waybill);
        if (temp == null) {
            throw new SupeWlydException("上报运单信息[" + waybill.getShippingNoteNumber() + "]没有找到");
        }

        //上报成功 才允许重新上报
        if (!StrUtil.equals(temp.getStatus(), UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode())) {
            return ResultMode.fail("运单[" + waybill.getShippingNoteNumber() + "]未上报成功，不支持重新上报");
        }

        //保存运单信息
        saveWaybillBusInfo(waybill);

        BeanUtil.copyProperties(waybill, temp, CopyOptions.create().ignoreNullValue().ignoreError());
        //上报
        ResultMode resultMode = SpvSpringFactory.getAopProxy(this).report(temp);
        if (resultMode.isSucceed()) {
            //手动上报成功，更新上报方式
            log.info("reportAgain#运单【{}】手动上报成功", temp.getShippingNoteNumber());
            waybillBusinessRepository.updateAutoReportStatus(
                temp.getShippingNoteNumber(), SuperviseEnmus.AutoReportStatusEnum.THIRTY.getCode(), "");
        }

        return resultMode;
    }

    /**
     * ToB业务资金同步
     * @param capitalFlowVo
     */
    @Override
    public void handleCapitalFlowMsg(SyncCapitalFlowVo capitalFlowVo) {
        //判断是否存在存在不处理
        SuperviseCapitalFlow superviseCapitalFlowDb = superviseCapitalFlowMapper.selectByDocumentNumber(capitalFlowVo.getDocumentNumber());
        if(ObjUtil.isNotNull(superviseCapitalFlowDb)){
            log.info("单证号:{},资金流水已存在不处理！",capitalFlowVo.getDocumentNumber());
            return;
        }
        SuperviseCapitalFlow superviseCapitalFlow = this.buildSuperviseCapitalFlow(capitalFlowVo);
        List<SuperviseCapitalFlowPayment> superviseCapitalFlowPayments = this.buildSuperviseCapitalFlowPayment(capitalFlowVo);
        List<SuperviseCapitalFlowWaybill> superviseCapitalFlowWaybills = this.buildSuperviseCapitalFlowWaybill(capitalFlowVo);
        List<Waybill> waybillUpdList = this.buildWaybillUpdList(superviseCapitalFlow, superviseCapitalFlowPayments, superviseCapitalFlowWaybills);

        WaybillServiceImpl waybillService = (WaybillServiceImpl) AopContext.currentProxy();
        waybillService.saveCapitalFlowInfo(superviseCapitalFlow,superviseCapitalFlowPayments,superviseCapitalFlowWaybills, waybillUpdList);

        //同步资金数据成功,回调bms更新上报状态
        for (SuperviseCapitalFlowWaybill superviseCapitalFlowWaybill : superviseCapitalFlowWaybills) {
            Boolean aBoolean = bmsSuperviseClient.updatePaymentReportStatus(superviseCapitalFlowWaybill.getShippingNoteNumber());
            if (aBoolean) {
                log.info("tob资金上报成功,更新bms上报状态成功,运单号:{}", superviseCapitalFlowWaybill.getShippingNoteNumber());
            } else {
                log.info("tob资金上报成功,更新bms上报状态失败,运单号:{}", superviseCapitalFlowWaybill.getShippingNoteNumber());
            }
        }
    }

    private List<Waybill> buildWaybillUpdList(SuperviseCapitalFlow superviseCapitalFlow,
                                              List<SuperviseCapitalFlowPayment> superviseCapitalFlowPayments,
                                              List<SuperviseCapitalFlowWaybill> superviseCapitalFlowWaybills) {
        List<Waybill> waybillUpdList = null;

        //获取配置中需要修改运输完成时间的货运主体id
        List<String> modifyTransportEndDateCustIds = channelConfigService.getModifyTransportEndDateCustIds();
        if (IterUtil.isNotEmpty(modifyTransportEndDateCustIds) && modifyTransportEndDateCustIds.contains(superviseCapitalFlow.getNetworkMainBodyId())) {
            List<String> shippingNoteNumberList = superviseCapitalFlowWaybills.stream()
                .map(SuperviseCapitalFlowWaybill::getShippingNoteNumber).collect(Collectors.toList());
            List<Waybill> waybills = waybillBusinessRepository.queryBusInfoByShippingNumberList(shippingNoteNumberList);
            if (IterUtil.isNotEmpty(waybills)) {
                waybillUpdList = new ArrayList<>();
                //最晚支付时间（目前就一条付款信息）
                Date maxPayDate = superviseCapitalFlowPayments.stream().map(SuperviseCapitalFlowPayment::getDateTime).max(Date::compareTo).get();

                for (Waybill waybill : waybills) {
                    Date transportEndDate = waybill.getTransportEndDate();
                    log.info("addCapitalAccountBusInfoAndAttribute#修改前：运输完结时间:{},支付时间：{}", transportEndDate, maxPayDate);
                    //运输完结时间 取随机值
                    Date randomTransportEndDate = SpvDateUtil.randomTransportEndDate(transportEndDate, maxPayDate);
                    if (ObjUtil.isNotNull(randomTransportEndDate)) {
                        log.info("addCapitalAccountBusInfoAndAttribute#修改后：运输完结时间:{},支付时间：{}", randomTransportEndDate, maxPayDate);
                        Waybill waybillUpd = new Waybill();
                        waybillUpd.setShippingNoteNumber(waybill.getShippingNoteNumber());
                        waybillUpd.setTransportEndDate(randomTransportEndDate);
                        waybillUpdList.add(waybillUpd);
                    }
                }
            }
        }

        return waybillUpdList;
    }

    /**
     * 保存资金流水
     * @param superviseCapitalFlow
     * @param superviseCapitalFlowPayments
     * @param superviseCapitalFlowWaybills
     * @param waybillUpdList
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCapitalFlowInfo(SuperviseCapitalFlow superviseCapitalFlow,
                                    List<SuperviseCapitalFlowPayment> superviseCapitalFlowPayments,
                                    List<SuperviseCapitalFlowWaybill> superviseCapitalFlowWaybills,
                                    List<Waybill> waybillUpdList) {

        if(!ObjUtil.isAllNotEmpty(superviseCapitalFlow,superviseCapitalFlowPayments,superviseCapitalFlowWaybills)){
            log.info("资金流水数据有为空的不处理");
            return;
        }
        superviseCapitalFlowMapper.insertSelective(superviseCapitalFlow);
        superviseCapitalFlowPaymentMapper.batchInsert(superviseCapitalFlowPayments);
        superviseCapitalFlowWaybillMapper.batchInsert(superviseCapitalFlowWaybills);
        // 更新运单中支付状态
        List<String> waybillIds = superviseCapitalFlowWaybills.stream()
            .map(waybill -> waybill.getShippingNoteNumber()).collect(Collectors.toList());
        List<List<String>> partitionWaybillIds = ListUtil.partition(waybillIds, 20);
        for (List<String> partitionWaybillId : partitionWaybillIds) {
            waybillBusinessRepository.batchUpdateByPayStatus(partitionWaybillId);
        }
        //更新运输完结时间
        if (IterUtil.isNotEmpty(waybillUpdList)) {
            waybillBusinessRepository.batchUpdateTransportEndDate(waybillUpdList);
        }
    }

    /**
     * 构建 SupervisCapitalFlow
     * @param capitalFlowVo
     * @return
     */
    private SuperviseCapitalFlow buildSuperviseCapitalFlow(SyncCapitalFlowVo capitalFlowVo) {
        SuperviseCapitalFlow superviseCapitalFlow = new SuperviseCapitalFlow()
            .setId(IdUtil.generateId()).setDocumentNumber(capitalFlowVo.getDocumentNumber())
            .setCarrier(capitalFlowVo.getCarrier()).setActualCarrierId(capitalFlowVo.getActualCarrierId())
            .setVehicleNumber(capitalFlowVo.getVehicleNumber()).setVehiclePlateColorCode(capitalFlowVo.getVehiclePlateColorCode())
            .setRemark(capitalFlowVo.getRemark()).setSuccessMessage("").setRiskLevel("100")
            .setAutoReportStatus("10").setVersionCode(0).setNetworkMainBodyName(capitalFlowVo.getNetworkMainBodyName())
            .setPayTotalAmount(capitalFlowVo.getAttributeVoList().stream().map(m ->m.getMonetaryAmount()).reduce(BigDecimal::add).get());


        //查询网络货运主体信息
        ResultMode<PlatformCmOperationMainBody> mainBodyResultMode = platformCmOperationMainBodyInter.getMainBody(capitalFlowVo.getNetworkMainBodyId());
        log.info("按照网络货运主体id:{},查询网络货运主体信息为:{}",capitalFlowVo.getNetworkMainBodyId(),JSONUtil.toJsonStr(mainBodyResultMode));
        if (mainBodyResultMode == null){
            throw new RuntimeException("上报资金调用platform服务获取平台物流交换代码异常,网络货运主体ID:{}" + capitalFlowVo.getNetworkMainBodyId());
        }
        if(mainBodyResultMode.getSucceed()&&mainBodyResultMode.getModel()!=null&&mainBodyResultMode.getModel().size()>0){
            PlatformCmOperationMainBody platformCmOperationMainBody=mainBodyResultMode.getModel().get(0);
            //设置平台物流交换代码
            superviseCapitalFlow.setMonitorPlatformCode(platformCmOperationMainBody.getMonitorPlatformCode());
        }
        superviseCapitalFlow.setStatus(UtilityEnum.SuperviseStatusEnum.SAVED.getCode());
        //解决平行帐号，获取真实网络货主体id
        superviseCapitalFlow.setNetworkMainBodyId(channelConfigService.getRealCustIdByExCustId(capitalFlowVo.getNetworkMainBodyId()));
        superviseCapitalFlow.setCreatedDate(new Date());
        superviseCapitalFlow.setCreatorId("sysadmin");
        superviseCapitalFlow.setUpdatedDate(new Date());
        superviseCapitalFlow.setUpdaterId("sysadmin");
        return superviseCapitalFlow;
    }

    /**
     * 构建 SuperviseCapitalFlowPayment
     * @param capitalFlowVo
     * @return
     */
    private List<SuperviseCapitalFlowPayment> buildSuperviseCapitalFlowPayment(SyncCapitalFlowVo capitalFlowVo) {

        List<SuperviseCapitalFlowPayment> superviseCapitalFlowPayments = new ArrayList<>();
        List<SyncCapitalFlowVo.SyncCapitalFlowPaymentVo> attributeVoList = capitalFlowVo.getAttributeVoList();
        for (SyncCapitalFlowVo.SyncCapitalFlowPaymentVo syncCapitalFlowPaymentVo : attributeVoList) {

            SuperviseCapitalFlowPayment superviseCapitalFlowPayment = new SuperviseCapitalFlowPayment()
                .setId(IdUtil.generateId()).setDocumentNumber(capitalFlowVo.getDocumentNumber())
                .setPaymentMeansCode(syncCapitalFlowPaymentVo.getPaymentMeansCode()).setRecipient(syncCapitalFlowPaymentVo.getRecipient())
                .setReceiptAccount(syncCapitalFlowPaymentVo.getReceiptAccount()).setBankCode(syncCapitalFlowPaymentVo.getBankCode())
                .setSequenceCode(syncCapitalFlowPaymentVo.getSequenceCode()).setMonetaryAmount(syncCapitalFlowPaymentVo.getMonetaryAmount())
                .setDateTime(syncCapitalFlowPaymentVo.getDateTime()).setCreatorId("sysadmin")
                .setCreatedDate(new Date()).setUpdaterId("sysadmin").setUpdatedDate(new Date()).setVersionCode(0);
            superviseCapitalFlowPayments.add(superviseCapitalFlowPayment);
        }
        return superviseCapitalFlowPayments;
    }

    /**
     * SuperviseCapitalFlowWaybill
     * @param capitalFlowVo
     * @return
     */
    private List<SuperviseCapitalFlowWaybill> buildSuperviseCapitalFlowWaybill(SyncCapitalFlowVo capitalFlowVo) {
        List<SuperviseCapitalFlowWaybill> superviseCapitalFlowWaybills = new ArrayList<>();
        List<SyncCapitalFlowVo.SyncCapitalFlowWaybillVo> waybillVoList = capitalFlowVo.getWaybillVoList();
        for (SyncCapitalFlowVo.SyncCapitalFlowWaybillVo syncCapitalFlowWaybillVo : waybillVoList) {
            SuperviseCapitalFlowWaybill superviseCapitalFlowWaybill = new SuperviseCapitalFlowWaybill()
                .setId(IdUtil.generateId()).setDocumentNumber(capitalFlowVo.getDocumentNumber())
                .setSerialNumber(syncCapitalFlowWaybillVo.getSerialNumber())
                .setShippingNoteNumber(syncCapitalFlowWaybillVo.getShippingNoteNumber())
                .setTotalMonetaryAmount(syncCapitalFlowWaybillVo.getTotalMonetaryAmount())
                .setCreatorId("sysadmin").setCreatedDate(new Date()).setUpdaterId("sysadmin")
                .setUpdatedDate(new Date()).setVersionCode(0);
            superviseCapitalFlowWaybills.add(superviseCapitalFlowWaybill);
        }
        return superviseCapitalFlowWaybills;
    }

    /**
     * 查询运单属性集合
     */
    @Override
    public List<WaybillAttribute> queryWaybillAttributeByShippingNumberList(List<String> shippingNoteNumberList) {
        if (IterUtil.isEmpty(shippingNoteNumberList)) {
            return null;
        }

        return waybillAttributeBusinessRepository.queryWaybillAttributeByShippingNumberList(shippingNoteNumberList);
    }

    /**
     * 失败数据自动上报
     */
    @Override
    public void autoReportError(List<String> custIds) {
        Set<String> waybillKeyList = RedisUtil.keys(SuperviseConstants.SUPERVISE_WAYBILL_REPORT_ERROR_REDIS_KEY + "*");
        if (IterUtil.isEmpty(waybillKeyList)) {
            log.info("autoReportError#waybill，无上报失败的运单");
            return;
        }

        waybillKeyList.stream().forEach(key -> {
            String waybillId = StrUtil.subAfter(key, SuperviseConstants.SUPERVISE_WAYBILL_REPORT_ERROR_REDIS_KEY, false);
            Waybill filter = new Waybill();
            filter.setShippingNoteNumber(waybillId);
            Waybill waybill = waybillBusinessRepository.viewWaybillBusInfo(filter);
            if (waybill == null) {
                log.info("autoReportError#waybill，未查询到运单信息:{}", waybillId);
                return;
            }
            if (!custIds.contains(waybill.getNetworkMainBodyId())) {
                log.info("autoReportError#waybill，运单【{}】主体未开启自动上报", waybillId);
                return;
            }

            if (StrUtil.equals(waybill.getStatus(), UtilityEnum.SuperviseStatusEnum.REPORT_SUCCESS.getCode())) {
                log.info("autoReportError#waybill，运单【{}】已上报成功", waybillId);
                RedisUtil.del(key);
                return;
            }
            if (!StrUtil.equals(waybill.getStatus(), UtilityEnum.SuperviseStatusEnum.REPORT_FAIL.getCode())) {
                log.info("autoReportError#waybill，运单【{}】未上报失败，上报状态:{}", waybillId, waybill.getStatus());
                return;
            }

            handelAutoReportWaybill(waybill);
        });
    }

    /**
     * 查询运单集合
     */
    @Override
    public List<Waybill> queryWaybillByShippingNumberList(List<String> shippingNoteNumberList) {
        if (IterUtil.isEmpty(shippingNoteNumberList)) {
            return null;
        }

        return waybillBusinessRepository.queryWaybillByShippingNumberList(shippingNoteNumberList);
    }

    @Override
    public void init(Waybill waybill) {
        if (ObjUtil.isNull(waybill) || StrUtil.isBlank(waybill.getShippingNoteNumber())) {
            return;
        }
        int status=10;
        try {
            //解决平行帐号，获取真实网络货主体id
            waybill.setNetworkMainBodyId(channelConfigService.getRealCustIdByExCustId(waybill.getNetworkMainBodyId()));
            addWaybillInfo(waybill);
        } catch (Exception e) {
            status=20;
            log.error("同步已签收的运单信息失败:{}", e);
        }
        saveKafka(KaFkaTopicNameEnmu.LGI_SYNC_SPV_WAYBILL_TOPIC.getTopicName(), waybill.getShippingNoteNumber(), JSONObject.toJSONString(waybill), status);
    }

    public void saveKafka(String topic, String businessId, String requestData, int status) {
        try {
            PlatformKafkaHandle platformKafkaHandle = new PlatformKafkaHandle();
            platformKafkaHandle.setTopicName(topic);
            platformKafkaHandle.setBusinessId(businessId);
            platformKafkaHandle.setRequestData(requestData);
            platformKafkaHandle.setStatus(status);
            platformExchangeService.saveKafka(platformKafkaHandle);
        } catch (Exception e) {
            log.error("同步保存异常信息失败", e);
        }
    }

    /**
     * 校验运单上报是否延迟时间
     * @param waybill
     * @param delayMinute
     * @return
     */
    private boolean checkWaybillDelayTime(Waybill waybill,Integer delayMinute){
        //查询明细
        WaybillAttribute waybillAttribute = waybillAttributeBusinessRepository.queryWaybillAttributeByShippingNumber(waybill.getShippingNoteNumber());
        if (waybillAttribute == null) {
            return false;
        }
        String drivingLicense = waybillAttribute.getDrivingLicense();
        String shippingNoteNumber = waybill.getShippingNoteNumber();
        String networkMainBodyId = waybill.getNetworkMainBodyId();
        //校验是否满足上报条件
        int driverCount = messageNoteService.checkIsDriverReportWitchDelay(drivingLicense, networkMainBodyId,delayMinute);
        if(driverCount<=0){
            String errorMsg = StrUtil.format(SupeStatusCodeEnum.BUSS_SUCCESS_BSUP0600.getMsg(), waybill.getShippingNoteNumber());
            StringBuffer stringBuffer = new StringBuffer()
                .append(SupeStatusCodeEnum.BUSS_SUCCESS_BSUP0600.getCode())
                .append(":")
                .append(errorMsg);
            log.info("autoReportWaybill#运单【{}】自动上报发生错误，错误信息：{}", shippingNoteNumber, stringBuffer.toString());
            //异常更新异常信息
            waybillBusinessRepository.updateAutoReportStatus(shippingNoteNumber, null, stringBuffer.toString());
            return false;
        }
        return true;
    }
}
