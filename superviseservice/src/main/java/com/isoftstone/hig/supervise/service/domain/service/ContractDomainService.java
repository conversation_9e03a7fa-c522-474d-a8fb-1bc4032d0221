package com.isoftstone.hig.supervise.service.domain.service;

import com.isoftstone.hig.supervise.service.domain.model.condition.ContractCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvContractBusInfoEntity;
import com.isoftstone.hig.supervise.service.domain.repository.ContractRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ContractDomainService {
    @Resource
    private ContractRepository contractRepository;

    public List<SpvContractBusInfoEntity> queryList(ContractCondition condition) {
        return contractRepository.queryList(condition);
    }

    /**
     * 删除
     */
    public void delete(Long id) {
        contractRepository.delete(id);
    }

    /**
     * 获取详情
     */
    public SpvContractBusInfoEntity getDetail(Long id) {
        return contractRepository.getDetail(id);
    }
}
