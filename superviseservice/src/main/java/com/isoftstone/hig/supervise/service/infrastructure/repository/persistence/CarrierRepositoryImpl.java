package com.isoftstone.hig.supervise.service.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.supervise.service.domain.model.condition.CarrierCondition;
import com.isoftstone.hig.supervise.service.domain.model.entity.SpvActualCarrierInfoEntity;
import com.isoftstone.hig.supervise.service.domain.repository.CarrierRepository;
import com.isoftstone.hig.supervise.service.infrastructure.repository.mapper.SpvActualCarrierInfoMapper;
import com.isoftstone.hig.supervise.service.infrastructure.repository.po.SpvActualCarrierInfoPO;
import com.isoftstone.hig.supervise.service.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class CarrierRepositoryImpl implements CarrierRepository {
    @Resource
    private SpvActualCarrierInfoMapper superviseCarrierBusInfoMapper;

    @Override
    public void init(SpvActualCarrierInfoEntity entity) {
        if(StrUtil.isEmpty(entity.getActualCarrierId())){
            return;
        }

        SpvActualCarrierInfoPO po = BeanUtil.toBean(entity, SpvActualCarrierInfoPO.class);
        po.setLastUpdateTime(DateUtil.date());

        List<SpvActualCarrierInfoEntity> list = queryList(new CarrierCondition(entity.getActualCarrierId()));
        if (CollUtil.isNotEmpty(list)) {
            po.setId(list.get(0).getId());
            superviseCarrierBusInfoMapper.updateById(po);
        } else {
            po.setId(IdUtil.generateId());
            po.setStatus(UtilityEnum.SuperviseStatusEnum.TO_DO.getCode());
            po.setCreateTime(DateUtil.date());
            superviseCarrierBusInfoMapper.insert(po);
        }
    }


    @Override
    public List<SpvActualCarrierInfoEntity> queryList(CarrierCondition condition) {
        LambdaQueryWrapper<SpvActualCarrierInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(condition.getActualCarrierName()), SpvActualCarrierInfoPO::getActualCarrierName, condition.getActualCarrierName());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getActualCarrierId()), SpvActualCarrierInfoPO::getActualCarrierId, condition.getActualCarrierId());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getContactMobile()), SpvActualCarrierInfoPO::getContactMobile, condition.getContactMobile());
        queryWrapper.eq(StrUtil.isNotBlank(condition.getStatus()), SpvActualCarrierInfoPO::getStatus, condition.getStatus());
        queryWrapper.ge(ObjUtil.isNotNull(condition.getStartSendToProDateTime()), SpvActualCarrierInfoPO::getSendToProDateTime, condition.getStartSendToProDateTime());
        queryWrapper.le(ObjUtil.isNotNull(condition.getEndSendToProDateTime()), SpvActualCarrierInfoPO::getSendToProDateTime, condition.getEndSendToProDateTime());
        queryWrapper.orderByDesc(SpvActualCarrierInfoPO::getCreateTime);
        List<SpvActualCarrierInfoPO> list = superviseCarrierBusInfoMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(list, SpvActualCarrierInfoEntity.class);
    }

    @Override
    public void delete(Long id) {
        SpvActualCarrierInfoPO po = new SpvActualCarrierInfoPO();
        po.setId(id);
        //设置为删除状态
        po.setStatus(UtilityEnum.SuperviseStatusEnum.DELETE.getCode());
        // TODO: 2025/7/2 编排传userId
        po.setLastUpdaterId(JwtUtil.getInstance().getUserBaseIdByToken());
        po.setLastUpdateTime(DateUtil.date());
        superviseCarrierBusInfoMapper.updateById(po);
    }

    @Override
    public SpvActualCarrierInfoEntity getDetail(Long id) {
        SpvActualCarrierInfoPO po = superviseCarrierBusInfoMapper.selectById(id);
        return BeanUtil.toBean(po, SpvActualCarrierInfoEntity.class);
    }

    @Override
    public void update(SpvActualCarrierInfoEntity entity) {
        SpvActualCarrierInfoPO po = BeanUtil.toBean(entity, SpvActualCarrierInfoPO.class);
        po.setLastUpdaterId(JwtUtil.getInstance().getUserBaseIdByToken());
        po.setLastUpdateTime(DateUtil.date());
        superviseCarrierBusInfoMapper.updateById(po);
    }

    @Override
    public SpvActualCarrierInfoEntity getByActualCarrierId(String actualCarrierId) {
        if (StrUtil.isBlank(actualCarrierId)) {
            return null;
        }

        LambdaQueryWrapper<SpvActualCarrierInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpvActualCarrierInfoPO::getActualCarrierId, actualCarrierId);
        return BeanUtil.toBean(superviseCarrierBusInfoMapper.selectOne(queryWrapper), SpvActualCarrierInfoEntity.class);
    }
}
