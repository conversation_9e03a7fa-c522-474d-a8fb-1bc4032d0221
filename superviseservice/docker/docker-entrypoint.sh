#!/bin/bash

JAVA_OPTS="-XX:+PrintCommandLineFlags -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/java_heapdump.hprof -Djava.security.egd=file:/dev/./urandom "
#JAVA_MEM_OPTS="-Xms1024m -Xmx1024m"

if [[ $JAVA_MEM_OPTS ]]; then
    JAVA_OPTS="$JAVA_OPTS $JAVA_MEM_OPTS"
fi

if [[ $LOGGER_LEVEL ]]; then
    JAVA_OPTS="$JAVA_OPTS -DlogLevel=$LOGGER_LEVEL"
fi

if [[ $JAVA_AGENT ]]; then
    JAVA_OPTS="$JAVA_AGENT $JAVA_OPTS"
fi

if [[ $APOLLO_ENV ]]; then
    JAVA_OPTS="$JAVA_OPTS -Denv=$APOLLO_ENV"
fi

if [[ $APP_NAME ]]; then
    JAVA_OPTS="$JAVA_OPTS -Dapp=$APP_NAME"
fi

echo 'JVM OPTIONS: '$JAVA_OPTS
java -server $JAVA_OPTS -jar /app.jar

